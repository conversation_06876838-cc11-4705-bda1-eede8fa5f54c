const express = require('express');
const router = express.Router();
const {
  listarAmbientes,
  buscarAmbientePorId,
  criarAmbiente,
  atualizarAmbiente,
  deletarAmbiente
} = require('../controllers/ambientesController');

// GET / - Lista todos os ambientes
router.get('/', listarAmbientes);

// GET /:id - Busca um ambiente pelo ID
router.get('/:id', buscarAmbientePorId);

// POST / - Cria um novo ambiente
router.post('/', criarAmbiente);

// PUT /:id - Atualiza um ambiente existente
router.put('/:id', atualizarAmbiente);

// DELETE /:id - Deleta um ambiente
router.delete('/:id', deletarAmbiente);

module.exports = router;
