const HistoricoStatusInspecao = require('../models/historicoStatusInspecaoModels');

// Lista todos os registros de histórico de status de inspeção
const listarHistorico = async (req, res) => {
  try {
    const historico = await HistoricoStatusInspecao.todos();
    res.json(historico);
  } catch (error) {
    console.error('Erro ao listar histórico de status de inspeção:', error);
    res.status(500).json({ error: 'Erro ao listar histórico de status de inspeção' });
  }
};

// Busca um registro específico por ID
const buscarPorId = async (req, res) => {
  const { id } = req.params;

  try {
    const registro = await HistoricoStatusInspecao.buscarPorId(id);
    if (!registro) {
      return res.status(404).json({ error: 'Registro de histórico de status não encontrado' });
    }
    res.json(registro);
  } catch (error) {
    console.error('Erro ao buscar histórico de status de inspeção:', error);
    res.status(500).json({ error: 'Erro ao buscar histórico de status de inspeção' });
  }
};

// Cria um novo registro
const criar = async (req, res) => {
  const { inspecao_id, status, data } = req.body;

  try {
    const novoRegistro = await HistoricoStatusInspecao.criar({ inspecao_id, status, data });
    res.status(201).json(novoRegistro);
  } catch (error) {
    console.error('Erro ao criar histórico de status de inspeção:', error);
    res.status(500).json({ error: 'Erro ao criar histórico de status de inspeção' });
  }
};

// Atualiza um registro existente
const atualizar = async (req, res) => {
  const { id } = req.params;
  const { inspecao_id, status, data } = req.body;

  try {
    const registroExistente = await HistoricoStatusInspecao.buscarPorId(id);
    if (!registroExistente) {
      return res.status(404).json({ error: 'Registro de histórico de status não encontrado' });
    }

    const registroAtualizado = await HistoricoStatusInspecao.atualizar(id, { inspecao_id, status, data });
    res.json(registroAtualizado);
  } catch (error) {
    console.error('Erro ao atualizar histórico de status de inspeção:', error);
    res.status(500).json({ error: 'Erro ao atualizar histórico de status de inspeção' });
  }
};

// Remove um registro
const deletar = async (req, res) => {
  const { id } = req.params;

  try {
    const sucesso = await HistoricoStatusInspecao.deletar(id);
    if (!sucesso) {
      return res.status(404).json({ error: 'Registro de histórico de status não encontrado' });
    }
    res.json({ message: 'Registro de histórico de status removido com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar histórico de status de inspeção:', error);
    res.status(500).json({ error: 'Erro ao deletar histórico de status de inspeção' });
  }
};

module.exports = {
  listarHistorico,
  buscarPorId,
  criar,
  atualizar,
  deletar,
};
