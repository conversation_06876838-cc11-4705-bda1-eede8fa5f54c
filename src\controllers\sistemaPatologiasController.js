const SistemaPatologia = require('../models/sistemaPatologiasModels');

// Lista todos os registros
const listarTodos = async (req, res) => {
  try {
    const lista = await SistemaPatologia.listarTodos();
    res.json(lista);
  } catch (error) {
    console.error('Erro ao listar sistema_patologias:', error);
    res.status(500).json({ error: 'Erro ao listar sistema_patologias' });
  }
};

// Busca por ID
const buscarPorId = async (req, res) => {
  const { id } = req.params;
  try {
    const item = await SistemaPatologia.buscarPorId(id);
    if (!item) {
      return res.status(404).json({ error: 'Registro não encontrado' });
    }
    res.json(item);
  } catch (error) {
    console.error('Erro ao buscar sistema_patologia:', error);
    res.status(500).json({ error: 'Erro ao buscar sistema_patologia' });
  }
};

// Cria novo registro
const criar = async (req, res) => {
  const { sistema_id, patologia_id } = req.body;
  try {
    const novo = await SistemaPatologia.criar({ sistema_id, patologia_id });
    res.status(201).json(novo);
  } catch (error) {
    console.error('Erro ao criar sistema_patologia:', error);
    res.status(500).json({ error: 'Erro ao criar sistema_patologia' });
  }
};

// Atualiza registro existente
const atualizar = async (req, res) => {
  const { id } = req.params;
  const { sistema_id, patologia_id } = req.body;
  try {
    const existente = await SistemaPatologia.buscarPorId(id);
    if (!existente) {
      return res.status(404).json({ error: 'Registro não encontrado' });
    }

    const atualizado = await SistemaPatologia.atualizar(id, { sistema_id, patologia_id });
    res.json(atualizado);
  } catch (error) {
    console.error('Erro ao atualizar sistema_patologia:', error);
    res.status(500).json({ error: 'Erro ao atualizar sistema_patologia' });
  }
};

// Deleta registro por ID
const deletar = async (req, res) => {
  const { id } = req.params;
  try {
    const deletado = await SistemaPatologia.deletar(id);
    if (!deletado) {
      return res.status(404).json({ error: 'Registro não encontrado' });
    }

    res.json({ message: 'Registro removido com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar sistema_patologia:', error);
    res.status(500).json({ error: 'Erro ao deletar sistema_patologia' });
  }
};

module.exports = {
  listarTodos,
  buscarPorId,
  criar,
  atualizar,
  deletar
};
