const db = require('../config/db');

class SistemaPatologia {
    static async listarTodos() {
        const result = await db.query('SELECT * FROM sistema_patologias');
        return result.rows;
    }

    static async criar({ sistema_id, patologia_id }) {
        const result = await db.query(
            'INSERT INTO sistema_patologias (sistema_id, patologia_id) VALUES ($1, $2) RETURNING *',
            [sistema_id, patologia_id]
        );
        return result.rows[0];
    }

    static async buscarPorId(id) {
        const result = await db.query('SELECT * FROM sistema_patologias WHERE id = $1', [id]);
        return result.rows[0];
    }

    static async atualizar(id, { sistema_id, patologia_id }) {
        const result = await db.query(
            'UPDATE sistema_patologias SET sistema_id = $1, patologia_id = $2 WHERE id = $3 RETURNING *',
            [sistema_id, patologia_id, id]
        );
        return result.rows[0];
    }

    static async deletar(id) {
        const result = await db.query('DELETE FROM sistema_patologias WHERE id = $1 RETURNING *', [id]);
        return result.rows[0];
    }
}

module.exports = SistemaPatologia;
