const db = require('../config/db');

class Registros {
    // Lista todos os registros com joins para obter dados relacionados
    static async todos() {
        const result = await db.query(`
            SELECT r.*, 
                i.nome as inspecao_nome,
                a.nome as ambiente_nome,
                sp.nome as sistema_patologia_nome,
                s.nome as status_nome
            FROM registros r
            LEFT JOIN inspecoes i ON r.inspecao_id = i.id
            LEFT JOIN ambientes a ON r.ambiente_id = a.id
            LEFT JOIN sistemas_patologias sp ON r.sistema_patologia_id = sp.id
            LEFT JOIN status s ON r.status_id = s.id
        `);
        return result.rows;
    }

    // Busca um registro pelo ID com dados relacionados
    static async buscarPorId(id) {
        const result = await db.query(`
            SELECT r.*, 
                i.nome as inspecao_nome,
                a.nome as ambiente_nome,
                sp.nome as sistema_patologia_nome,
                s.nome as status_nome
            FROM registros r
            LEFT JOIN inspecoes i ON r.inspecao_id = i.id
            LEFT JOIN ambientes a ON r.ambiente_id = a.id
            LEFT JOIN sistemas_patologias sp ON r.sistema_patologia_id = sp.id
            LEFT JOIN status
            WHERE r.id = $1
        `, [id]);
        return result.rows[0];
    }

    // Os métodos criar, atualizar e deletar permanecem iguais pois já tratam corretamente as foreign keys
    static async criar(registro) {
        const result = await db.query(
            `INSERT INTO registros(cabecalho, torre_bloco, inspecao_id, ambiente_id, sistema_patologia_id, status, data_registro, observacoes) 
             VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`,
            [registro.cabecalho, registro.torre_bloco, registro.inspecao_id, registro.ambiente_id, registro.sistema_patologia_id, registro.status, registro.data_registro, registro.observacoes]
        );
        return result.rows[0];
    }

    static async atualizar(id, dados) {
        const result = await db.query(
            `UPDATE registros 
             SET cabecalho = $1, torre_bloco = $2, inspecao_id = $3, ambiente_id = $4, 
                     sistema_patologia_id = $5, status = $6, data_registro = $7, observacoes = $8 
             WHERE id = $9 RETURNING *`,
            [dados.cabecalho, dados.torre_bloco, dados.inspecao_id, dados.ambiente_id, dados.sistema_patologia_id, dados.status, dados.data_registro, dados.observacoes, id]
        );
        return result.rows[0];
    }

    static async deletar(id) {
        const result = await db.query('DELETE FROM registros WHERE id = $1 RETURNING *', [id]);
        return result.rowCount > 0;
    }
}

module.exports = Registros;