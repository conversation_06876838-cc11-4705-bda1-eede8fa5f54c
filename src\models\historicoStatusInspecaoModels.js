// A tabela "historico_status_inspecao" armazena os status registrados ao longo do tempo
// para cada inspeção, permitindo um acompanhamento histórico das mudanças.

const db = require('../config/db');

class HistoricoStatusInspecao {
  static async todos() {
    const result = await db.query('SELECT * FROM historico_status_inspecao');
    return result.rows;
  }

  static async buscarPorId(id) {
    const result = await db.query('SELECT * FROM historico_status_inspecao WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async criar(data) {
    const result = await db.query(
      'INSERT INTO historico_status_inspecao (inspecao_id, status, data) VALUES ($1, $2, $3) RETURNING *',
      [data.inspecao_id, data.status, data.data]
    );
    return result.rows[0];
  }

  static async atualizar(id, data) {
    const result = await db.query(
      'UPDATE historico_status_inspecao SET inspecao_id = $1, status = $2, data = $3 WHERE id = $4 RETURNING *',
      [data.inspecao_id, data.status, data.data, id]
    );
    return result.rows[0];
  }

  static async deletar(id) {
    const result = await db.query('DELETE FROM historico_status_inspecao WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = HistoricoStatusInspecao;
