<!DOCTYPE html>
<html>
<head>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <title>Teste do Banco de Dados</title>
    <style>
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>Testar conexão com o Banco de Dados</h1>

    <button onclick="testarBanco()">Listar Tabelas</button>

    <div id="status" class="hidden"></div>

    <div id="tabelas-container" class="hidden">
        <h2>Tabelas do Banco de Dados</h2>
        <table border="1">
            <tr>
                <th>#</th>
                <th>Nome da Tabela</th>
                <th>Ações</th>
            </tr>
            <tbody id="tabelas-body">
            </tbody>
        </table>
    </div>

    <div id="detalhes-container" class="hidden">
        <a onclick="voltarParaTabelas()">Voltar</a>

        <h2 id="tabela-nome">Detalhes da Tabela</h2>

        <div id="tabela-status" class="hidden"></div>

        <div id="estrutura-container" class="hidden">
            <h3>Estrutura</h3>
            <table border="1">
                <tr>
                    <th>Coluna</th>
                    <th>Tipo</th>
                </tr>
                <tbody id="estrutura-body">
                </tbody>
            </table>
        </div>

        <div id="dados-container" class="hidden">
            <h3>Dados (<span id="registros-count">0</span> registros)</h3>
            <table border="1">
                <thead id="dados-head">
                </thead>
                <tbody id="dados-body">
                </tbody>
            </table>
            <div id="dados-metadata"></div>
        </div>
    </div>

    <script>
        function testarBanco() {
            var statusEl = document.getElementById('status');
            statusEl.innerHTML = 'Conectando...';
            statusEl.classList.remove('hidden');

            document.getElementById('tabelas-container').classList.add('hidden');
            document.getElementById('detalhes-container').classList.add('hidden');

            fetch('/testar-banco')
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    statusEl.innerHTML = data.status;

                    var tabelasBody = document.getElementById('tabelas-body');
                    tabelasBody.innerHTML = '';

                    if (data.tabelas && data.tabelas.length > 0) {
                        for (var i = 0; i < data.tabelas.length; i++) {
                            var tabela = data.tabelas[i];
                            var row = document.createElement('tr');

                            var indexCell = document.createElement('td');
                            indexCell.textContent = i + 1;
                            row.appendChild(indexCell);

                            var nomeCell = document.createElement('td');
                            nomeCell.textContent = tabela;
                            row.appendChild(nomeCell);

                            var acoesCell = document.createElement('td');
                            var verBtn = document.createElement('button');
                            verBtn.textContent = 'Ver';
                            verBtn.onclick = (function(t) {
                                return function() {
                                    consultarTabela(t);
                                };
                            })(tabela);
                            acoesCell.appendChild(verBtn);
                            row.appendChild(acoesCell);

                            tabelasBody.appendChild(row);
                        }

                        document.getElementById('tabelas-container').classList.remove('hidden');
                    } else {
                        statusEl.innerHTML = 'Nenhuma tabela encontrada';
                    }
                })
                .catch(function(error) {
                    statusEl.innerHTML = 'Erro: ' + error;
                });
        }

        function consultarTabela(nomeTabela) {
            document.getElementById('tabelas-container').classList.add('hidden');
            document.getElementById('detalhes-container').classList.remove('hidden');

            document.getElementById('tabela-nome').textContent = 'Tabela: ' + nomeTabela;

            var statusEl = document.getElementById('tabela-status');
            statusEl.innerHTML = 'Carregando...';
            statusEl.classList.remove('hidden');

            document.getElementById('estrutura-container').classList.add('hidden');
            document.getElementById('dados-container').classList.add('hidden');

            fetch('/consultar-tabela/' + nomeTabela)
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    statusEl.classList.add('hidden');

                    // Estrutura
                    var estruturaBody = document.getElementById('estrutura-body');
                    estruturaBody.innerHTML = '';

                    if (data.estrutura && data.estrutura.length > 0) {
                        for (var i = 0; i < data.estrutura.length; i++) {
                            var coluna = data.estrutura[i];
                            var row = document.createElement('tr');

                            var nomeCell = document.createElement('td');
                            nomeCell.textContent = coluna.column_name;
                            row.appendChild(nomeCell);

                            var tipoCell = document.createElement('td');
                            tipoCell.textContent = coluna.data_type;
                            row.appendChild(tipoCell);

                            estruturaBody.appendChild(row);
                        }

                        document.getElementById('estrutura-container').classList.remove('hidden');
                    }

                    // Dados
                    var dadosHead = document.getElementById('dados-head');
                    var dadosBody = document.getElementById('dados-body');

                    dadosHead.innerHTML = '';
                    dadosBody.innerHTML = '';

                    if (data.dados && data.dados.length > 0) {
                        // Cabeçalho
                        var headerRow = document.createElement('tr');
                        var colunas = Object.keys(data.dados[0]);

                        for (var i = 0; i < colunas.length; i++) {
                            var th = document.createElement('th');
                            th.textContent = colunas[i];
                            headerRow.appendChild(th);
                        }

                        dadosHead.appendChild(headerRow);

                        // Dados
                        for (var i = 0; i < data.dados.length; i++) {
                            var registro = data.dados[i];
                            var row = document.createElement('tr');

                            for (var j = 0; j < colunas.length; j++) {
                                var td = document.createElement('td');
                                var valor = registro[colunas[j]];

                                if (valor === null) {
                                    td.innerHTML = 'NULL';
                                } else {
                                    td.textContent = valor;
                                }

                                row.appendChild(td);
                            }

                            dadosBody.appendChild(row);
                        }

                        document.getElementById('registros-count').textContent = data.total_registros;
                        document.getElementById('dados-metadata').textContent = '';
                    } else {
                        var headerRow = document.createElement('tr');
                        var th = document.createElement('th');
                        th.textContent = 'Sem dados';
                        headerRow.appendChild(th);
                        dadosHead.appendChild(headerRow);

                        var emptyRow = document.createElement('tr');
                        var td = document.createElement('td');
                        td.textContent = 'Tabela vazia';
                        emptyRow.appendChild(td);
                        dadosBody.appendChild(emptyRow);

                        document.getElementById('registros-count').textContent = '0';
                    }

                    document.getElementById('dados-container').classList.remove('hidden');
                })
                .catch(function(error) {
                    statusEl.innerHTML = 'Erro: ' + error;
                });
        }

        function voltarParaTabelas() {
            document.getElementById('detalhes-container').classList.add('hidden');
            document.getElementById('tabelas-container').classList.remove('hidden');
        }

        window.onload = testarBanco;
    </script>
</body>
</html>
