const Ambiente = require('../models/ambientesModels');

// Lista todos os ambientes
const listarAmbientes = async (req, res) => {
  try {
    const ambientes = await Ambiente.todos();
    res.status(200).json(ambientes);
  } catch (error) {
    console.error('Erro ao listar ambientes:', error);
    res.status(500).json({ erro: 'Erro ao listar ambientes.', detalhes: error.message });
  }
};

// Busca um ambiente pelo ID
const buscarAmbientePorId = async (req, res) => {
  try {
    const ambiente = await Ambiente.buscarPorId(req.params.id);
    if (ambiente) {
      res.status(200).json(ambiente);
    } else {
      res.status(404).json({ erro: 'Ambiente não encontrado.' });
    }
  } catch (error) {
    console.error('Erro ao buscar ambiente:', error);
    res.status(500).json({ erro: 'Erro ao buscar ambiente.', detalhes: error.message });
  }
};

// Cria um novo ambiente
const criarAmbiente = async (req, res) => {
  try {
    const novoAmbiente = await Ambiente.criar(req.body);
    res.status(201).json(novoAmbiente);
  } catch (error) {
    console.error('Erro ao criar ambiente:', error);
    res.status(500).json({ erro: 'Erro ao criar ambiente.', detalhes: error.message });
  }
};

// Atualiza um ambiente existente
const atualizarAmbiente = async (req, res) => {
  try {
    const ambienteAtualizado = await Ambiente.atualizar(req.params.id, req.body);
    if (ambienteAtualizado) {
      res.status(200).json(ambienteAtualizado);
    } else {
      res.status(404).json({ erro: 'Ambiente não encontrado para atualização.' });
    }
  } catch (error) {
    console.error('Erro ao atualizar ambiente:', error);
    res.status(500).json({ erro: 'Erro ao atualizar ambiente.', detalhes: error.message });
  }
};

// Deleta um ambiente pelo ID
const deletarAmbiente = async (req, res) => {
  try {
    const sucesso = await Ambiente.deletar(req.params.id);
    if (sucesso) {
      res.status(200).json({ mensagem: 'Ambiente deletado com sucesso.' });
    } else {
      res.status(404).json({ erro: 'Ambiente não encontrado para exclusão.' });
    }
  } catch (error) {
    console.error('Erro ao deletar ambiente:', error);
    res.status(500).json({ erro: 'Erro ao deletar ambiente.', detalhes: error.message });
  }
};

module.exports = {
  listarAmbientes,
  buscarAmbientePorId,
  criarAmbiente,
  atualizarAmbiente,
  deletarAmbiente,
};
