const db = require('../config/db');

class Projetos {
  // Lista todos os projetos
  static async todos() {
    const result = await db.query('SELECT * FROM projetos');
    return result.rows;
  }

  // Busca um projeto pelo ID
  static async buscarPorId(id) {
    const result = await db.query('SELECT * FROM projetos WHERE id = $1', [id]);
    return result.rows[0];
  }

  // Cria um novo projeto
  static async criar(projeto) {
    const result = await db.query(
      'INSERT INTO projetos (cabecalho, edificio_id, coordenador_id, data_inicio, data_fim) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [projeto.cabecalho, projeto.edificio_id, projeto.coordenador_id, projeto.data_inicio, projeto.data_fim]
    );
    return result.rows[0];
  }

  // Atualiza um projeto existente
  static async atualizar(id, dados) {
    const result = await db.query(
      'UPDATE projetos SET cabecalho = $1, edificio_id = $2, coordenador_id = $3, data_inicio = $4, data_fim = $5 WHERE id = $6 RETURNING *',
      [dados.cabecalho, dados.edificio_id, dados.coordenador_id, dados.data_inicio, dados.data_fim, id]
    );
    return result.rows[0];
  }

  // Deleta um projeto pelo ID
  static async deletar(id) {
    const result = await db.query('DELETE FROM projetos WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = Projetos;