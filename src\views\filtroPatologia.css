
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Estilo do corpo */
body {
  font-family: sans-serif;
  background-color: #fff;
  color: #333;
  line-height: 1.6;
}

/* Cabeçalho */
header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  font-weight: bold;
  background-color: #f5f5f5;
}

.back {
  font-size: 24px;
  cursor: pointer;
}

/* Container principal */
.container {
  padding: 16px;
  max-width: 480px;
  margin: 0 auto;
}

h2 {
  margin-top: 0;
  font-size: 20px;
}

/* Acordeão */
.accordion {
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
}

.accordion-header {
  background: #e0e0e0;
  padding: 12px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.accordion-body {
  display: none;
  padding: 12px;
  background: #f9f9f9;
}

.accordion-body div {
  padding: 8px 0;
  cursor: pointer;
  color: #555;
}

.accordion-body div:hover {
  background-color: #eee;
}

.selected {
  background-color: #d0e9ff;
  font-weight: bold;
}

/* Link para criar nova patologia */
.create-link {
  font-size: 14px;
  margin-top: 8px;
}

.create-link a {
  color: #0099ff;
  text-decoration: none;
}

.create-link a:hover {
  text-decoration: underline;
}

/* Navegação inferior */
.bottom-nav {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: space-around;
  padding: 12px 0;
  border-top: 1px solid #ddd;
}

.bottom-nav div {
  text-align: center;
  font-size: 14px;
  cursor: pointer;
}

.bottom-nav img {
  width: 24px;
  height: auto;
  display: block;
  margin: 0 auto 4px;
}
