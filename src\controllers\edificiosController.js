// Importa o model que acessa o banco de dados para edifícios
const Edificio = require('../models/edificiosModels');

// Listar todos os edifícios
const listarEdificios = async (req, res) => {
  try {
    const edificios = await Edificio.todos();
    res.status(200).json(edificios);
  } catch (error) {
    console.error('Erro ao buscar edifícios:', error);
    res.status(500).json({ error: 'Erro ao buscar edifícios.' });
  }
};

// Buscar edifício por ID
const buscarEdificioPorId = async (req, res) => {
  try {
    const edificio = await Edificio.buscarPorId(req.params.id);
    if (edificio) {
      res.status(200).json(edificio);
    } else {
      res.status(404).json({ error: 'Edifício não encontrado.' });
    }
  } catch (error) {
    console.error('Erro ao buscar edifício:', error);
    res.status(500).json({ error: 'Erro ao buscar edifício.' });
  }
};

// Buscar edifícios por termo (nome, título ou endereço)
const buscarEdificioPorTermo = async (req, res) => {
  try {
    const termo = req.query.termo;
    if (!termo) {
      return res.status(400).json({ error: 'Parâmetro "termo" é obrigatório.' });
    }
    const resultados = await Edificio.buscarPorTermo(termo);
    res.status(200).json(resultados);
  } catch (error) {
    console.error('Erro ao buscar edifícios por termo:', error);
    res.status(500).json({ error: 'Erro ao buscar edifícios por termo.' });
  }
};

// Criar um novo edifício
const criarEdificio = async (req, res) => {
  try {
    const novo = await Edificio.criar(req.body);
    res.status(201).json(novo);
  } catch (error) {
    console.error('Erro ao criar edifício:', error);
    res.status(500).json({ error: 'Erro ao criar edifício.' });
  }
};

// Atualizar um edifício existente
const atualizarEdificio = async (req, res) => {
  try {
    const atualizado = await Edificio.atualizar(req.params.id, req.body);
    if (atualizado) {
      res.status(200).json(atualizado);
    } else {
      res.status(404).json({ error: 'Edifício não encontrado para atualização.' });
    }
  } catch (error) {
    console.error('Erro ao atualizar edifício:', error);
    res.status(500).json({ error: 'Erro ao atualizar edifício.' });
  }
};

// Deletar um edifício
const deletarEdificio = async (req, res) => {
  try {
    const deletado = await Edificio.deletar(req.params.id);
    if (deletado) {
      res.status(204).send();
    } else {
      res.status(404).json({ error: 'Edifício não encontrado para exclusão.' });
    }
  } catch (error) {
    console.error('Erro ao deletar edifício:', error);
    res.status(500).json({ error: 'Erro ao deletar edifício.' });
  }
};

module.exports = {
  listarEdificios,
  buscarEdificioPorId,
  buscarEdificioPorTermo,
  criarEdificio,
  atualizarEdificio,
  deletarEdificio,
};
