// A tabela "etapas_inspecao" representa as diferentes etapas de uma inspeção predial,
// incluindo datas de início e fim de cada fase.

const db = require('../config/db');

class EtapaInspecao {
  static async todos() {
    const result = await db.query('SELECT * FROM etapas_inspecao');
    return result.rows;
  }

  static async buscarPorId(id) {
    const result = await db.query('SELECT * FROM etapas_inspecao WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async criar(data) {
    const result = await db.query(
      'INSERT INTO etapas_inspecao (inspecao_id, etapa, data_inicio, data_fim) VALUES ($1, $2, $3, $4) RETURNING *',
      [data.inspecao_id, data.etapa, data.data_inicio, data.data_fim]
    );
    return result.rows[0];
  }

  static async atualizar(id, data) {
    const result = await db.query(
      'UPDATE etapas_inspecao SET inspecao_id = $1, etapa = $2, data_inicio = $3, data_fim = $4 WHERE id = $5 RETURNING *',
      [data.inspecao_id, data.etapa, data.data_inicio, data.data_fim, id]
    );
    return result.rows[0];
  }

  static async deletar(id) {
    const result = await db.query('DELETE FROM etapas_inspecao WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = EtapaInspecao;
