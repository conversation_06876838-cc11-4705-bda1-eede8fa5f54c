<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Perfil</title>
  <style>
    * {
      box-sizing: border-box;
    }
    body {
      margin: 0;
      font-family: sans-serif;
      background: #fff;
      padding-bottom: 120px;
    }

    header {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px;
      font-weight: bold;
      font-size: 1.3rem;
      border-bottom: 1px solid #ddd;
      position: relative;
    }

    .back-button {
      position: absolute;
      left: 16px;
      font-size: 20px;
      background: none;
      border: none;
      cursor: pointer;
    }

    .container {
      max-width: 480px;
      margin: 0 auto;
      padding: 1rem;
    }

    .profile {
      display: flex;
      gap: 1rem;
      align-items: center;
      margin-bottom: 1rem;
    }

    .profile-img {
      width: 96px;
      height: 96px;
      object-fit: cover;
      border-radius: 8px;
    }

    .profile-info {
      flex: 1;
    }

    .profile-info label {
      display: block;
      font-size: 0.9rem;
      margin-bottom: 4px;
      font-weight: bold;
    }

    .profile-info input {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 8px;
      margin-bottom: 10px;
      font-size: 1rem;
    }

    .section-title {
      margin-top: 24px;
      margin-bottom: 12px;
      font-size: 1.1rem;
      font-weight: bold;
      text-align: center;
      border-top: 1px solid #ddd;
      padding-top: 12px;
    }

    .inspection-card {
      display: flex;
      gap: 1rem;
      align-items: center;
      background: #f5f5f5;
      border-radius: 12px;
      padding: 12px;
      margin-bottom: 12px;
      cursor: pointer;
    }

    .inspection-img {
      width: 100px;
      height: 70px;
      object-fit: cover;
      border-radius: 8px;
    }

    .inspection-content h3 {
      margin: 0;
      font-size: 1rem;
    }

    .inspection-content p {
      margin: 4px 0 0;
      font-size: 0.9rem;
    }

    .status {
      font-size: 0.8rem;
      padding: 4px 8px;
      border-radius: 8px;
      margin-left: 8px;
    }

    .status-nao {
      background: #666;
      color: white;
    }

    .status-andamento {
      background: #f4a300;
      color: white;
    }

    .bottom-nav {
      position: fixed;
      bottom: 0;
      width: 100%;
      background-color: #f5f5f5;
      display: flex;
      justify-content: space-around;
      padding: 12px 0;
      border-top: 1px solid #ddd;
    }

    .bottom-nav div {
      text-align: center;
      font-size: 14px;
      cursor: pointer;
    }

    .bottom-nav img {
      width: 24px;
      height: auto;
      display: block;
      margin: 0 auto 4px;
    }
  </style>
</head>
<body>

  <header>
    <button class="back-button" onclick="history.back()">←</button>
    Perfil
  </header>

  <main class="container">
    <section class="profile">
      <label for="uploadFoto">
        <img src="../assets/img/perfil.jpg" alt="Foto do usuário" class="profile-img" id="fotoPerfil">
      </label>
      <input type="file" id="uploadFoto" accept="image/*" style="display: none" onchange="previewFoto(event)">
      <div class="profile-info">
        <label for="nome">Nome Completo</label>
        <input type="text" id="nome" placeholder="Digite seu nome">

        <label for="email">Email</label>
        <input type="email" id="email" placeholder="<EMAIL>">
      </div>
    </section>

    <h2 class="section-title">Inspeções em andamento</h2>

    <section class="inspection-card" onclick="location.href='registro.html'">
      <img src="/assets/img/predio1.jpg" alt="Imagem inspeção" class="inspection-img">

      <div class="inspection-content">
        <h3>Inspeção BPO</h3>
        <p>22/08/2024 <span class="status status-nao">Não Iniciado</span></p>
      </div>
    </section>

    <section class="inspection-card" onclick="location.href='registro.html'">
      <img src="/assets/img/predio1.jpg" alt="Imagem inspeção" class="inspection-img">
      <div class="inspection-content">
        <h3>Inspeção xpto</h3>
        <p>22/08/2024 <span class="status status-andamento">Em Andamento</span></p>
      </div>
    </section>
  </main>

  <footer class="bottom-nav">
    <div onclick="location.href='inspecoes.html'">
      <img src="/assets/Icons/pasteS.png" alt="Inspeções">
      <div>Inspeções</div>
    </div>
    <div onclick="location.href='perfil.html'">
      <img src="/assets/Icons/profileS.png" alt="Perfil">
      <div>Perfil</div>
    </div>
  </footer>

  <script>
    function previewFoto(event) {
      const foto = document.getElementById('fotoPerfil');
      foto.src = URL.createObjectURL(event.target.files[0]);
    }

    document.addEventListener("DOMContentLoaded", () => {
      const nomeInput = document.getElementById('nome');
      const emailInput = document.getElementById('email');

      const nomeSalvo = localStorage.getItem('perfilNome');
      const emailSalvo = localStorage.getItem('perfilEmail');
      if (nomeSalvo) nomeInput.value = nomeSalvo;
      if (emailSalvo) emailInput.value = emailSalvo;

      nomeInput.addEventListener('input', () => {
        localStorage.setItem('perfilNome', nomeInput.value);
      });

      emailInput.addEventListener('input', () => {
        localStorage.setItem('perfilEmail', emailInput.value);
      });
    });
  </script>
</body>
</html>
