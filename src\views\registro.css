
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Estilo do corpo */
body {
  font-family: sans-serif;
  background-color: #fff;
  color: #333;
  line-height: 1.6;
}

/* Cabeçalho */
header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  font-weight: bold;
  background-color: #f5f5f5;
}

.close {
  font-size: 24px;
  cursor: pointer;
}

/* Container principal */
.container {
  padding: 16px;
  padding-bottom: 100px;
  max-width: 480px;
  margin: 0 auto;
}

/* Upload de imagem */
.image-upload {
  border: 1px solid #ccc;
  border-radius: 8px;
  text-align: center;
  padding: 24px;
  margin-bottom: 24px;
  position: relative;
}

.plus-circle {
  display: block;
  margin: 0 auto 8px;
  width: 80px;
  height: 80px;
  cursor: pointer;
}

.plus-circle img {
  width: 100%;
  height: auto;
}

.image-upload input[type="file"] {
  display: none;
}

#preview {
  width: 100%;
  max-height: 200px;
  object-fit: contain;
  display: none;
  margin-top: 12px;
}

/* Formulários */
label {
  font-weight: bold;
  display: block;
  margin-top: 16px;
  margin-bottom: 8px;
}

input,
textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 8px;
}

.arrow {
  text-align: right;
  margin-top: -40px;
  margin-bottom: 16px;
  padding-right: 8px;
  font-size: 18px;
}

/* Botão Criar */
.btn {
  width: 100%;
  border: none;
  padding: 0;
  background: none;
  margin-top: 24px;
  cursor: pointer;
}

.btn img {
  width: 100%;
  max-width: 300px;
  display: block;
  margin: 0 auto;
}

/* Navegação inferior */
.bottom-nav {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: space-around;
  padding: 12px 0;
  border-top: 1px solid #ddd;
}

.bottom-nav div {
  text-align: center;
  font-size: 14px;
  cursor: pointer;
}

.bottom-nav img {
  width: 24px;
  height: auto;
  display: block;
  margin: 0 auto 4px;
}
