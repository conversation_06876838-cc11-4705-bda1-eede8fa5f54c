<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Inspeções</title>
  <style>
    * { box-sizing: border-box; }
    body {
      font-family: sans-serif;
      margin: 0;
      padding: 0;
      background: #fff;
      padding-bottom: 80px;
    }

    header {
      text-align: center;
      padding: 16px;
      font-size: 1.5rem;
      font-weight: bold;
      border-bottom: 1px solid #ddd;
    }

    main {
      max-width: 480px;
      margin: 0 auto;
    }

    .filtros {
      display: flex;
      justify-content: space-around;
      flex-wrap: wrap;
      padding: 1rem;
    }

    .filtros button {
      border: none;
      background: #f0f0f0;
      padding: 0.5rem 1rem;
      border-radius: 999px;
      font-size: 0.9rem;
      cursor: pointer;
    }

    .filtros button.ativo {
      background-color: #44799c;
      color: white;
    }

    .pesquisa {
      padding: 0 1rem;
    }

    .pesquisa input {
      width: 100%;
      padding: 0.8rem;
      border-radius: 12px;
      border: none;
      background: #f1f1f1;
      font-size: 1rem;
    }

    .inspecao {
      margin: 1rem;
      border-radius: 12px;
      overflow: hidden;
      cursor: pointer;
      border: 1px solid #ddd;
    }

    .inspecao img {
      width: 100%;
      height: auto;
      object-fit: cover;
    }

    .conteudo {
      padding: 0.8rem;
      background-color: #f5f7fa;
    }

    .conteudo h3 {
      margin: 0;
      font-size: 1.1rem;
    }

    .info {
      display: flex;
      justify-content: space-between;
      margin-top: 0.5rem;
      font-size: 0.9rem;
      align-items: center;
    }

    .tag {
      padding: 0.3rem 0.6rem;
      border-radius: 8px;
      font-size: 0.75rem;
      color: white;
    }

    .nao-iniciado { background-color: #666; }

    .bottom-nav {
      position: fixed;
      bottom: 0;
      width: 100%;
      background-color: #f5f5f5;
      display: flex;
      justify-content: space-around;
      padding: 12px 0;
      border-top: 1px solid #ddd;
    }

    .bottom-nav div {
      text-align: center;
      font-size: 14px;
      cursor: pointer;
    }

    .bottom-nav img {
      width: 24px;
      height: auto;
      display: block;
      margin: 0 auto 4px;
    }

    .oculto { display: none; }
  </style>
</head>
<body>

  <header>Inspeções</header>

  <main>
    <div class="filtros">
      <button class="ativo" onclick="filtrar('todos')">Todos</button>
      <button onclick="filtrar('a-fazer')">A Fazer</button>
      <button onclick="filtrar('em-andamento')">Em Andamento</button>
      <button onclick="filtrar('concluido')">Concluído</button>
    </div>

    <div class="pesquisa">
      <input type="text" placeholder="Pesquisar">
    </div>

    <!-- Card A Fazer -->
    <div class="inspecao a-fazer" onclick="window.location.href='inspecao.html'">
      <img id="img-inspecao" src="https://via.placeholder.com/400x200" alt="Imagem">
      <div class="conteudo">
        <h3>Inspeção BPO</h3>
        <div class="info">
          <span>22/08/2024</span>
          <span class="tag nao-iniciado">Não Iniciado</span>
        </div>
      </div>
    </div>

    <!-- Card Em Andamento -->
    <div class="inspecao em-andamento" onclick="alert('Futuramente abrirá a tela da inspeção em andamento')">
      <img src="https://via.placeholder.com/400x200/f4a300/ffffff?text=Em+Andamento" alt="Imagem inspeção em andamento">
      <div class="conteudo" style="background:#f4a300; color:white;">
        <h3>Inspeção Elétrica</h3>
        <div class="info">
          <span>21/08/2024</span>
          <span class="tag">Em Andamento</span>
        </div>
      </div>
    </div>

    <!-- Card Concluído -->
    <div class="inspecao concluido" onclick="alert('Futuramente abrirá a tela da inspeção concluída')">
      <img src="https://via.placeholder.com/400x200/003366/ffffff?text=Concluído" alt="Imagem inspeção concluída">
      <div class="conteudo" style="background:#003366; color:white;">
        <h3>Inspeção Concluída</h3>
        <div class="info">
          <span>20/08/2024</span>
          <span class="tag">Concluído</span>
        </div>
      </div>
    </div>
  </main>

  <div class="bottom-nav">
    <div onclick="location.href='inspecoes.html'">
      <img src="/assets/Icons/paste.png" alt="Inspeções">
      <div>Inspeções</div>
    </div>
    <div onclick="location.href='perfil.html'">
      <img src="/assets/Icons/profile.png" alt="Perfil">
      <div>Perfil</div>
    </div>
  </div>

  <script>
    function filtrar(tipo) {
      document.querySelectorAll('.filtros button').forEach(btn => btn.classList.remove('ativo'));
      document.querySelector(`.filtros button[onclick*="${tipo}"]`).classList.add('ativo');

      document.querySelectorAll('.inspecao').forEach(card => {
        if (tipo === 'todos') {
          card.classList.remove('oculto');
        } else {
          card.classList.toggle('oculto', !card.classList.contains(tipo));
        }
      });
    }

    // Carrega imagem salva no localStorage
    document.addEventListener("DOMContentLoaded", () => {
      const imagem = localStorage.getItem("imagemInspecao");
      if (imagem) {
        const imgTag = document.getElementById("img-inspecao");
        imgTag.src = imagem;
      }
    });
  </script>

</body>
</html>
