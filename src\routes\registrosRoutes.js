const express = require('express');
const router = express.Router();
const registrosController = require('../controllers/registrosController');

router.get('/', registrosController.listarTodos);
router.get('/:id', registrosController.buscarPorId);
router.post('/', registrosController.criar);
router.put('/:id', registrosController.atualizar);
router.delete('/:id', registrosController.deletar);

module.exports = router;
