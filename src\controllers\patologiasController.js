const Patologias = require('../models/patologiasModels');

// Lista todas as patologias
const listarTodos = async (req, res) => {
  try {
    const patologias = await Patologias.listarTodos();
    res.json(patologias);
  } catch (error) {
    console.error('Erro ao listar patologias:', error);
    res.status(500).json({ error: 'Erro ao listar patologias' });
  }
};

// Busca uma patologia pelo ID
const buscarPorId = async (req, res) => {
  const { id } = req.params;

  try {
    const patologia = await Patologias.buscarPorId(id);
    if (!patologia) {
      return res.status(404).json({ error: 'Patologia não encontrada' });
    }
    res.json(patologia);
  } catch (error) {
    console.error('Erro ao buscar patologia:', error);
    res.status(500).json({ error: 'Erro ao buscar patologia' });
  }
};

// Cria uma nova patologia
const criar = async (req, res) => {
  const { descricao, categoria_id } = req.body;

  try {
    const novaPatologia = await Patologias.criar({ descricao, categoria_id });
    res.status(201).json(novaPatologia);
  } catch (error) {
    console.error('Erro ao criar patologia:', error);
    res.status(500).json({ error: 'Erro ao criar patologia' });
  }
};

// Atualiza uma patologia existente
const atualizar = async (req, res) => {
  const { id } = req.params;
  const { descricao, categoria_id } = req.body;

  try {
    const patologiaExistente = await Patologias.buscarPorId(id);
    if (!patologiaExistente) {
      return res.status(404).json({ error: 'Patologia não encontrada' });
    }

    const patologiaAtualizada = await Patologias.atualizar(id, { descricao, categoria_id });
    res.json(patologiaAtualizada);
  } catch (error) {
    console.error('Erro ao atualizar patologia:', error);
    res.status(500).json({ error: 'Erro ao atualizar patologia' });
  }
};

// Deleta uma patologia pelo ID
const deletar = async (req, res) => {
  const { id } = req.params;

  try {
    const patologiaDeletada = await Patologias.deletar(id);
    if (!patologiaDeletada) {
      return res.status(404).json({ error: 'Patologia não encontrada' });
    }
    res.json({ message: 'Patologia removida com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar patologia:', error);
    res.status(500).json({ error: 'Erro ao deletar patologia' });
  }
};

module.exports = {
  listarTodos,
  buscarPorId,
  criar,
  atualizar,
  deletar,
};
