const db = require('../config/db');

class Edificio {
  // Lista todos os edifícios
  static async todos() {
    const result = await db.query('SELECT * FROM edificios');
    return result.rows;
  }

  // Busca um edifício pelo ID
  static async buscarPorId(id) {
    const result = await db.query('SELECT * FROM edificios WHERE id = $1', [id]);
    return result.rows[0];
  }

  // Busca edifícios por nome, título da inspeção ou id do endereço (parcial e case-insensitive)
  static async buscarPorTermo(termo) {
    const result = await db.query(
      `SELECT * FROM edificios 
       WHERE nome ILIKE $1 
          OR titulo_inspecao ILIKE $1
          OR CAST(endereco AS TEXT) ILIKE $1`,
      [`%${termo}%`]
    );
    return result.rows;
  }

  // Insere um novo edifício
  static async criar(edificio) {
    const result = await db.query(
      `INSERT INTO edificios 
        (titulo_inspecao, nome, endereco, data_inicio, data_fim, tipo, status, criado_por)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
       RETURNING *`,
      [
        edificio.titulo_inspecao,
        edificio.nome,
        edificio.endereco,       // id da tabela endereco
        edificio.data_inicio,
        edificio.data_fim,
        edificio.tipo,
        edificio.status,         
        edificio.criado_por      // id do usuário
      ]
    );
    return result.rows[0];
  }

  // Atualiza um edifício existente
  static async atualizar(id, dados) {
    const result = await db.query(
      `UPDATE edificios
       SET titulo_inspecao = $1,
           nome = $2,
           endereco = $3,
           data_inicio = $4,
           data_fim = $5,
           tipo = $6,
           status = $7,
           criado_por = $8
       WHERE id = $9
       RETURNING *`,
      [
        dados.titulo_inspecao,
        dados.nome,
        dados.endereco,
        dados.data_inicio,
        dados.data_fim,
        dados.tipo,
        dados.status,
        dados.criado_por,
        id
      ]
    );
    return result.rows[0];
  }

  // Deleta um edifício pelo ID
  static async deletar(id) {
    const result = await db.query('DELETE FROM edificios WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = Edificio;
