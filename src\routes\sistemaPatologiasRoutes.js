const express = require('express');
const router = express.Router();
const sistemaPatologiaController = require('../controllers/sistemaPatologiasController');

router.get('/', sistemaPatologiaController.listarTodos);
router.get('/:id', sistemaPatologiaController.buscarPorId);
router.post('/', sistemaPatologiaController.criar);
router.put('/:id', sistemaPatologiaController.atualizar);
router.delete('/:id', sistemaPatologiaController.deletar);

module.exports = router;
