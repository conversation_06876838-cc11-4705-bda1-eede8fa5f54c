const db = require('../config/db');

class Inspecao {
  // Lista todas as inspeções
  static async todos() {
    const result = await db.query('SELECT * FROM inspecoes');
    return result.rows;
  }

  // Busca uma inspeção pelo ID
  static async buscarPorId(id) {
    const result = await db.query('SELECT * FROM inspecoes WHERE id = $1', [id]);
    return result.rows[0];
  }

  // Cria uma nova inspeção
  static async criar(inspecao) {
    const result = await db.query(
      `INSERT INTO inspecoes (projeto_id, criado_por, status)
       VALUES ($1, $2, $3)
       RETURNING *`,
      [inspecao.projeto_id, inspecao.criado_por, inspecao.status]
    );
    return result.rows[0];
  }

  // Atualiza uma inspeção existente
  static async atualizar(id, dados) {
    const result = await db.query(
      `UPDATE inspecoes 
       SET projeto_id = $1,
           criado_por = $2,
           status = $3
       WHERE id = $4
       RETURNING *`,
      [dados.projeto_id, dados.criado_por, dados.status, id]
    );
    return result.rows[0];
  }

  // Deleta uma inspeção pelo ID
  static async deletar(id) {
    const result = await db.query('DELETE FROM inspecoes WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = Inspecao;
