// O model "Relatorio" representa os relatórios gerados a partir das inspeções.
// Cada relatório possui um responsável pelo envio e outro pela análise,
// além de estar vinculado diretamente a uma inspeção.

const db = require('../config/db');

class Relatorio {
  // Lista todos os relatórios com JOINs para nome de quem enviou e analisou, e status da inspeção
  static async todos() {
    const result = await db.query(`
      SELECT
        r.*,
        u1.nome AS nome_enviado_por,
        u2.nome AS nome_analisado_por,
        i.status AS status_inspecao
      FROM relatorios r
      LEFT JOIN usuarios u1 ON r.enviado_por = u1.id
      LEFT JOIN usuarios u2 ON r.analisado_por = u2.id
      LEFT JOIN inspecoes i ON r.inspecao_id = i.id
    `);
    return result.rows;
  }

  // Busca um relatório específico com JOINs também
  static async buscarPorId(id) {
    const result = await db.query(`
      SELECT
        r.*,
        u1.nome AS nome_enviado_por,
        u2.nome AS nome_analisado_por,
        i.status AS status_inspecao
      FROM relatorios r
      LEFT JOIN usuarios u1 ON r.enviado_por = u1.id
      LEFT JOIN usuarios u2 ON r.analisado_por = u2.id
      LEFT JOIN inspecoes i ON r.inspecao_id = i.id
      WHERE r.id = $1
    `, [id]);
    return result.rows[0];
  }

  // Cria um novo relatório
  static async criar(data) {
    const result = await db.query(
      'INSERT INTO relatorios (inspecao_id, enviado_por, analisado_por, status, data_envio, data_analise, observacoes) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *',
      [data.inspecao_id, data.enviado_por, data.analisado_por, data.status, data.data_envio, data.data_analise, data.observacoes]
    );
    return result.rows[0];
  }

  // Atualiza um relatório
  static async atualizar(id, data) {
    const result = await db.query(
      'UPDATE relatorios SET inspecao_id = $1, enviado_por = $2, analisado_por = $3, status = $4, data_envio = $5, data_analise = $6, observacoes = $7 WHERE id = $8 RETURNING *',
      [data.inspecao_id, data.enviado_por, data.analisado_por, data.status, data.data_envio, data.data_analise, data.observacoes, id]
    );
    return result.rows[0];
  }

  // Deleta um relatório
  static async deletar(id) {
    const result = await db.query('DELETE FROM relatorios WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = Relatorio;
