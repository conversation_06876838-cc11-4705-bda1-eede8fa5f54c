const Sistemas = require('../models/sistemasModels');

// Lista todos os sistemas
const listarTodos = async (req, res) => {
  try {
    const lista = await Sistemas.todos();
    res.json(lista);
  } catch (error) {
    console.error('Erro ao listar sistemas:', error);
    res.status(500).json({ error: 'Erro ao listar sistemas' });
  }
};

// Busca um sistema por ID
const buscarPorId = async (req, res) => {
  const { id } = req.params;
  try {
    const sistema = await Sistemas.buscarPorId(id);
    if (!sistema) {
      return res.status(404).json({ error: 'Sistema não encontrado' });
    }
    res.json(sistema);
  } catch (error) {
    console.error('Erro ao buscar sistema:', error);
    res.status(500).json({ error: 'Erro ao buscar sistema' });
  }
};

// Cria um novo sistema
const criar = async (req, res) => {
  const { nome } = req.body;

  if (!nome) {
    return res.status(400).json({ error: 'O campo "nome" é obrigatório' });
  }

  try {
    const novoSistema = await Sistemas.criar({ nome });
    res.status(201).json(novoSistema);
  } catch (error) {
    console.error('Erro ao criar sistema:', error);
    res.status(500).json({ error: 'Erro ao criar sistema' });
  }
};

// Atualiza um sistema existente
const atualizar = async (req, res) => {
  const { id } = req.params;
  const { nome } = req.body;

  if (!nome) {
    return res.status(400).json({ error: 'O campo "nome" é obrigatório' });
  }

  try {
    const existente = await Sistemas.buscarPorId(id);
    if (!existente) {
      return res.status(404).json({ error: 'Sistema não encontrado' });
    }

    const atualizado = await Sistemas.atualizar(id, { nome });
    res.json(atualizado);
  } catch (error) {
    console.error('Erro ao atualizar sistema:', error);
    res.status(500).json({ error: 'Erro ao atualizar sistema' });
  }
};

// Deleta um sistema
const deletar = async (req, res) => {
  const { id } = req.params;

  try {
    const deletado = await Sistemas.deletar(id);
    if (!deletado) {
      return res.status(404).json({ error: 'Sistema não encontrado' });
    }
    res.json({ message: 'Sistema deletado com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar sistema:', error);
    res.status(500).json({ error: 'Erro ao deletar sistema' });
  }
};

module.exports = {
  listarTodos,
  buscarPorId,
  criar,
  atualizar,
  deletar
};
