const db = require('../config/db');

class Anexo {
  // Lista todos os anexos
  static async todos() {
    const result = await db.query('SELECT * FROM anexos');
    return result.rows;
  }

  // Busca um anexo pelo ID
  static async buscarPorId(id) {
    const result = await db.query('SELECT * FROM anexos WHERE id = $1', [id]);
    return result.rows[0];
  }

  // Insere um novo anexo
  static async criar(anexo) {
    const result = await db.query(
      'INSERT INTO anexos (caminho_arquivo, legenda) VALUES ($1, $2) RETURNING *',
      [anexo.caminho_arquivo, anexo.legenda]
    );
    return result.rows[0];
  }

  // Atualiza um anexo existente
  static async atualizar(id, dados) {
    const result = await db.query(
      'UPDATE anexos SET caminho_arquivo = $1, legenda = $2 WHERE id = $3 RETURNING *',
      [dados.caminho_arquivo, dados.legenda, id]
    );
    return result.rows[0];
  }

  // Deleta um anexo pelo ID
  static async deletar(id) {
    const result = await db.query('DELETE FROM anexos WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = Anexo;
