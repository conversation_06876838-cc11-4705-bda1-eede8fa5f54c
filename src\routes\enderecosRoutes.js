const express = require('express');
const router = express.Router();
const enderecoController = require('../controllers/enderecoController');

// GET /enderecos - Lista todos os endereços
router.get('/', enderecoController.listarTodos);

// GET /enderecos/:id - Busca um endereço pelo ID
router.get('/:id', enderecoController.buscarPorId);

// POST /enderecos - Cria um novo endereço
router.post('/', enderecoController.criar);

// PUT /enderecos/:id - Atualiza um endereço existente
router.put('/:id', enderecoController.atualizar);

// DELETE /enderecos/:id - Remove um endereço
router.delete('/:id', enderecoController.deletar);

module.exports = router;

