require('dotenv').config();
const express = require('express');
const app = express();
const { Pool } = require('pg');
const path = require('path');

app.use(express.urlencoded({ extended: true }));
app.use('/assets', express.static(path.join(__dirname, 'src/assets')));

app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.use(express.static(path.join(__dirname, 'src/views')));

// Configuração do pool de conexões
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_DATABASE,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
  ssl: process.env.DB_SSL === 'true' ? {
    rejectUnauthorized: false
  } : false
});

// Teste de conexão com o banco de dados
pool.connect()
  .then(client => {
    console.log('Conectado ao banco de dados PostgreSQL');
    client.release();

    // importar rotas
    const ambientesRoutes = require('../2025-1B-T16-IN02-G03/src/routes/ambienteRoutes');
    const anexosRoutes = require('../2025-1B-T16-IN02-G03/src/routes/anexosRoutes');
    const categoriaPatologiaRoutes = require('../2025-1B-T16-IN02-G03/src/routes/categoriaPatologiaRoutes');
    const edificiosRoutes = require('../2025-1B-T16-IN02-G03/src/routes/edificiosRoutes');
    const enderecosRoutes = require('../2025-1B-T16-IN02-G03/src/routes/enderecosRoutes');
    const etapasInspecaoRoutes = require('../2025-1B-T16-IN02-G03/src/routes/etapasInspecaoRoutes');
    const historicoStatusRoutes = require('../2025-1B-T16-IN02-G03/src/routes/historicoStatusRoutes');
    const inspecoesRoutes = require('../2025-1B-T16-IN02-G03/src/routes/inspecoesRoutes');
    const patologiaAnexosRoutes = require('../2025-1B-T16-IN02-G03/src/routes/patologiaAnexosRoutes');
    const patologiasRoutes = require('../2025-1B-T16-IN02-G03/src/routes/patologiasRoutes');
    const projetosRoutes = require('../2025-1B-T16-IN02-G03/src/routes/projetosRoutes');
    const registrosRoutes = require('../2025-1B-T16-IN02-G03/src/routes/registrosRoutes');
    const sistemaPatologiasRoutes = require('../2025-1B-T16-IN02-G03/src/routes/sistemaPatologiasRoutes');
    const sistemaRoutes = require('../2025-1B-T16-IN02-G03/src/routes/sistemasRoutes');
    const usuariosProjetosRoutes = require('../2025-1B-T16-IN02-G03/src/routes/usuariosProjetosRoutes');
    const usuariosRoutes = require('../2025-1B-T16-IN02-G03/src/routes/usuariosRoutes');

    // usar rotas com prefixos diferentes
    app.use('/ambientes', ambientesRoutes);
    app.use('/anexos', anexosRoutes);
    app.use('/categoria', categoriaPatologiaRoutes);
    app.use('/edificios', edificiosRoutes);
    app.use('/enderecos', enderecosRoutes);
    app.use('/etapas', etapasInspecaoRoutes);
    app.use('/historico', historicoStatusRoutes);
    app.use('/inspecoes', inspecoesRoutes);
    app.use('/patologiasAnexos', patologiaAnexosRoutes);
    app.use('/patologias', patologiasRoutes);
    app.use('/projetos', projetosRoutes);
    app.use('/registros', registrosRoutes);
    app.use('/sistemaPatologias', sistemaPatologiasRoutes);
    app.use('/sistema', sistemaRoutes);
    app.use('/usuariosProjetos', usuariosProjetosRoutes);
    app.use('/usuarios', usuariosRoutes);
    app.use('/relatorios', relatorioRoutes);


    // Middleware para lidar com erros de rota não encontrada
    app.use((req, res, next) => {
      res.status(404).send('Página não encontrada');
    });

    // Middleware para lidar com erros internos do servidor
    app.use((err, req, res, next) => {
      console.error(err.stack);
      res.status(500).send('Erro no servidor');
    });

    // Iniciar o servidor
    const PORT = process.env.PORT || 3000;
    app.listen(PORT, () => {
      console.log(`Servidor rodando na porta ${PORT}`);
    });
  })
  .catch(err => {
    console.error('Erro ao conectar ao banco de dados:', err);
  });