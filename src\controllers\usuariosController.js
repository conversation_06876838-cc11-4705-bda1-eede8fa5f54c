const Usuarios = require('../models/usuariosModels');
const bcrypt = require('bcrypt');
// Lista todos os usuários
const listarTodos = async (req, res) => {
  try {
    const usuarios = await Usuarios.todos();
    res.json(usuarios);
  } catch (error) {
    console.error('Erro ao listar usuários:', error);
    res.status(500).json({ error: 'Erro ao listar usuários' });
  }
};

// Busca um usuário por ID
const buscarPorId = async (req, res) => {
  const { id } = req.params;
  try {
    const usuario = await Usuarios.buscarPorId(id);
    if (!usuario) {
      return res.status(404).json({ error: 'Usuário não encontrado' });
    }
    res.json(usuario);
  } catch (error) {
    console.error('Erro ao buscar usuário:', error);
    res.status(500).json({ error: 'Erro ao buscar usuário' });
  }
};

// Cria um novo usuário
const criar = async (req, res) => {
  const { nome, email, senha } = req.body;

  if (!nome || !email || !senha) {
    return res.status(400).json({ error: 'Nome, e-mail e senha são obrigatórios' });
  }

  try {
    const usuarioExistente = await Usuarios.buscarPorEmail(email);
    if (usuarioExistente) {
      return res.status(400).json({ error: 'E-mail já cadastrado' });
    }

    // Criptografa a senha antes de salvar
    const senhaCriptografada = await bcrypt.hash(senha, 10);

    const novoUsuario = await Usuarios.criar({ nome, email, senha: senhaCriptografada });
    res.status(201).json(novoUsuario);
  } catch (error) {
    console.error('Erro ao criar usuário:', error);
    res.status(500).json({ error: 'Erro ao criar usuário' });
  }
};

// Faz o login do usuário
const login = async (req, res) => {
  const { email, senha } = req.body;

  if (!email || !senha) {
    // Mostra mensagem de erro simples na tela
    return res.send('<p style="color:red;">E-mail e senha são obrigatórios</p><a href="/login.html">Voltar</a>');
  }

  try {
    const usuario = await Usuarios.buscarPorEmail(email);
    if (!usuario) {
      return res.send('<p style="color:red;">E-mail ou senha inválidos</p><a href="/login.html">Voltar</a>');
    }

    const senhaCorreta = await bcrypt.compare(senha, usuario.senha);
    if (!senhaCorreta) {
      return res.send('<p style="color:red;">E-mail ou senha inválidos</p><a href="/login.html">Voltar</a>');
    }

    // Login bem-sucedido: redireciona para a dashboard
    res.redirect('/telaInicial.html');
  } catch (error) {
    console.error('Erro ao fazer login:', error);
    res.status(500).send('<p style="color:red;">Erro ao fazer login</p><a href="/login.html">Voltar</a>');
  }
};

// Atualiza um usuário
const atualizar = async (req, res) => {
  const { id } = req.params;
  const { nome, email, senha } = req.body;

  if (!nome || !email || !senha) {
    return res.status(400).json({ error: 'Nome, e-mail e senha são obrigatórios' });
  }

  try {
    const usuario = await Usuarios.buscarPorId(id);
    if (!usuario) {
      return res.status(404).json({ error: 'Usuário não encontrado' });
    }

    const usuarioAtualizado = await Usuarios.atualizar(id, { nome, email, senha });
    res.json(usuarioAtualizado);
  } catch (error) {
    console.error('Erro ao atualizar usuário:', error);
    res.status(500).json({ error: 'Erro ao atualizar usuário' });
  }
};

// Deleta um usuário
const deletar = async (req, res) => {
  const { id } = req.params;

  try {
    const sucesso = await Usuarios.deletar(id);
    if (!sucesso) {
      return res.status(404).json({ error: 'Usuário não encontrado' });
    }
    res.json({ message: 'Usuário removido com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar usuário:', error);
    res.status(500).json({ error: 'Erro ao deletar usuário' });
  }
};

module.exports = {
  listarTodos,
  buscarPorId,
  criar,
  atualizar,
  deletar,
  login
};