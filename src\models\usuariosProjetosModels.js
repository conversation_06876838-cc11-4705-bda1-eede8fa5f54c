// A tabela "usuarios_projetos" representa o vínculo entre usuários e projetos.
// Cada linha indica que um determinado usuário participa de um projeto.
// O campo "permissao_id" não existe mais no novo modelo.

const db = require('../config/db');

class UsuariosProjetos {
  // Lista todos os vínculos entre usuários e projetos
  static async todos() {
    const result = await db.query('SELECT * FROM usuarios_projetos');
    return result.rows;
  }

  // Busca um vínculo específico pelo ID
  static async buscarPorId(id) {
    const result = await db.query('SELECT * FROM usuarios_projetos WHERE id = $1', [id]);
    return result.rows[0];
  }

  // Cria um novo vínculo entre usuário e projeto
  static async criar({ usuario_id, projeto_id }) {
    const result = await db.query(
      'INSERT INTO usuarios_projetos (usuario_id, projeto_id) VALUES ($1, $2) RETURNING *',
      [usuario_id, projeto_id]
    );
    return result.rows[0];
  }

  // Atualiza o projeto associado a um vínculo
  static async atualizar(id, { usuario_id, projeto_id }) {
    const result = await db.query(
      'UPDATE usuarios_projetos SET usuario_id = $1, projeto_id = $2 WHERE id = $3 RETURNING *',
      [usuario_id, projeto_id, id]
    );
    return result.rows[0];
  }

  // Remove um vínculo entre usuário e projeto
  static async deletar(id) {
    const result = await db.query('DELETE FROM usuarios_projetos WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = UsuariosProjetos;
