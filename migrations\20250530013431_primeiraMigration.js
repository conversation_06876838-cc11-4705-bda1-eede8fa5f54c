/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema
    // Primeiro as tabelas independentes
    .createTable('usuarios', function(table) {
      table.increments('id').primary();
      table.string('nome', 100).notNullable();
      table.string('email', 100).unique().notNullable();
      table.string('senha', 100).notNullable();
    })
    .createTable('enderecos', function(table) {
      table.increments('id').primary();
      table.string('cep', 9);
      table.string('rua', 200);
      table.string('bairro', 200);
      table.integer('num');
      table.string('cidade', 100);
      table.string('estado', 100);
    })
    .createTable('edificios', function(table) {
      table.increments('id').primary();
      table.string('titulo_inspecao', 100);
      table.string('nome', 100);
      table.integer('endereco_id').unsigned().references('id').inTable('enderecos');
      table.date('data_inicio');
      table.date('data_fim');
      table.string('tipo', 100);
      table.string('status', 20);
      table.integer('criado_por').unsigned().references('id').inTable('usuarios');
    })
    .createTable('projetos', function(table) {
      table.increments('id').primary();
      table.string('cabecalho', 100);
      table.integer('edificio_id').unsigned().references('id').inTable('edificios');
      table.integer('coordenador_id').unsigned().references('id').inTable('usuarios');
      table.date('data_inicio');
      table.date('data_fim');
    })
    .createTable('inspecoes', function(table) {
      table.increments('id').primary();
      table.integer('projeto_id').unsigned().references('id').inTable('projetos').notNullable();
      table.integer('criado_por').unsigned().references('id').inTable('usuarios').notNullable();
      table.string('status', 50).notNullable();
      table.timestamps(true, true); // Adiciona created_at e updated_at
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema
    .dropTable('inspecoes')
    .dropTable('projetos')
    .dropTable('edificios')
    .dropTable('enderecos')
    .dropTable('usuarios');
};
