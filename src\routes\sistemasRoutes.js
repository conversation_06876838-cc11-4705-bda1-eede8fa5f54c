const express = require('express');
const router = express.Router();
const sistemasController = require('../controllers/sistemasController');

router.get('/', sistemasController.listarTodos);
router.get('/:id', sistemasController.buscarPorId);
router.post('/', sistemasController.criar);
router.put('/:id', sistemasController.atualizar);
router.delete('/:id', sistemasController.deletar);

module.exports = router;
