const express = require('express');
const router = express.Router();
const {
  listarEdificios,
  buscarEdificioPorId,
  buscarEdificioPorTermo,
  criarEdificio,
  atualizarEdificio,
  deletarEdificio,
} = require('../controllers/edificiosController');

// Lista todos os edifícios
router.get('/', listarEdificios);

// Busca edifícios por termo (query string: ?termo=...)
router.get('/buscar', buscarEdificioPorTermo);

// Busca um edifício específico por ID
router.get('/:id', buscarEdificioPorId);

// Cria um novo edifício
router.post('/', criarEdificio);

// Atualiza um edifício existente
router.put('/:id', atualizarEdificio);

// Deleta um edifício
router.delete('/:id', deletarEdificio);

module.exports = router;
