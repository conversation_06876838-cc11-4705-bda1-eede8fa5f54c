const Relatorio = require('../models/relatorioModels');

// Lista todos os relatórios
const listarTodos = async (req, res) => {
  try {
    const relatorios = await Relatorio.todos();
    res.json(relatorios);
  } catch (error) {
    console.error('Erro ao listar relatórios:', error);
    res.status(500).json({ error: 'Erro ao listar relatórios' });
  }
};

// Busca relatório por ID
const buscarPorId = async (req, res) => {
  const { id } = req.params;

  try {
    const relatorio = await Relatorio.buscarPorId(id);
    if (!relatorio) {
      return res.status(404).json({ error: 'Relatório não encontrado' });
    }
    res.json(relatorio);
  } catch (error) {
    console.error('Erro ao buscar relatório:', error);
    res.status(500).json({ error: 'Erro ao buscar relatório' });
  }
};

// Cria novo relatório
const criar = async (req, res) => {
  const {
    inspecao_id,
    enviado_por,
    analisado_por,
    status,
    data_envio,
    data_analise,
    observacoes
  } = req.body;

  try {
    const novoRelatorio = await Relatorio.criar({
      inspecao_id,
      enviado_por,
      analisado_por,
      status,
      data_envio,
      data_analise,
      observacoes
    });
    res.status(201).json(novoRelatorio);
  } catch (error) {
    console.error('Erro ao criar relatório:', error);
    res.status(500).json({ error: 'Erro ao criar relatório' });
  }
};

// Atualiza relatório existente
const atualizar = async (req, res) => {
  const { id } = req.params;
  const {
    inspecao_id,
    enviado_por,
    analisado_por,
    status,
    data_envio,
    data_analise,
    observacoes
  } = req.body;

  try {
    const relatorioExistente = await Relatorio.buscarPorId(id);
    if (!relatorioExistente) {
      return res.status(404).json({ error: 'Relatório não encontrado' });
    }

    const relatorioAtualizado = await Relatorio.atualizar(id, {
      inspecao_id,
      enviado_por,
      analisado_por,
      status,
      data_envio,
      data_analise,
      observacoes
    });

    res.json(relatorioAtualizado);
  } catch (error) {
    console.error('Erro ao atualizar relatório:', error);
    res.status(500).json({ error: 'Erro ao atualizar relatório' });
  }
};

// Deleta relatório por ID
const deletar = async (req, res) => {
  const { id } = req.params;

  try {
    const relatorioDeletado = await Relatorio.deletar(id);
    if (!relatorioDeletado) {
      return res.status(404).json({ error: 'Relatório não encontrado' });
    }

    res.json({ message: 'Relatório removido com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar relatório:', error);
    res.status(500).json({ error: 'Erro ao deletar relatório' });
  }
};

module.exports = {
  listarTodos,
  buscarPorId,
  criar,
  atualizar,
  deletar
};
