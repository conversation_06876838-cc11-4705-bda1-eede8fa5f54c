const Inspecao = require('../models/inspecoesModels');

// Lista todas as inspeções
const listarInspecoes = async (req, res) => {
  try {
    const inspecoes = await Inspecao.todos();
    res.json(inspecoes);
  } catch (error) {
    console.error('Erro ao listar inspeções:', error);
    res.status(500).json({ error: 'Erro ao listar inspeções' });
  }
};

// Busca uma inspeção pelo ID
const buscarPorId = async (req, res) => {
  const { id } = req.params;

  try {
    const inspecao = await Inspecao.buscarPorId(id);
    if (!inspecao) {
      return res.status(404).json({ error: 'Inspeção não encontrada' });
    }
    res.json(inspecao);
  } catch (error) {
    console.error('Erro ao buscar inspeção:', error);
    res.status(500).json({ error: 'Erro ao buscar inspeção' });
  }
};

// Cria uma nova inspeção
const criar = async (req, res) => {
  const { projeto_id, criado_por, status } = req.body;

  try {
    const novaInspecao = await Inspecao.criar({ projeto_id, criado_por, status });
    res.status(201).json(novaInspecao);
  } catch (error) {
    console.error('Erro ao criar inspeção:', error);
    res.status(500).json({ error: 'Erro ao criar inspeção' });
  }
};

// Atualiza uma inspeção existente
const atualizar = async (req, res) => {
  const { id } = req.params;
  const { projeto_id, criado_por, status } = req.body;

  try {
    const inspecaoExistente = await Inspecao.buscarPorId(id);
    if (!inspecaoExistente) {
      return res.status(404).json({ error: 'Inspeção não encontrada' });
    }

    const inspecaoAtualizada = await Inspecao.atualizar(id, { projeto_id, criado_por, status });
    res.json(inspecaoAtualizada);
  } catch (error) {
    console.error('Erro ao atualizar inspeção:', error);
    res.status(500).json({ error: 'Erro ao atualizar inspeção' });
  }
};

// Deleta uma inspeção pelo ID
const deletar = async (req, res) => {
  const { id } = req.params;

  try {
    const sucesso = await Inspecao.deletar(id);
    if (!sucesso) {
      return res.status(404).json({ error: 'Inspeção não encontrada' });
    }
    res.json({ message: 'Inspeção removida com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar inspeção:', error);
    res.status(500).json({ error: 'Erro ao deletar inspeção' });
  }
};

module.exports = {
  listarInspecoes,
  buscarPorId,
  criar,
  atualizar,
  deletar,
};
