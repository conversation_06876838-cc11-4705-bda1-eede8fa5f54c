const express = require('express');
const router = express.Router();
const relatoriosController = require('../controllers/relatoriosController');

router.get('/', relatoriosController.listarTodos);
router.get('/:id', relatoriosController.buscarPorId);
router.post('/', relatoriosController.criar);
router.put('/:id', relatoriosController.atualizar);
router.delete('/:id', relatoriosController.deletar);

module.exports = router;
