const { Pool } = require('pg');
require('dotenv').config();

// Configuração do pool de conexões
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_DATABASE,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
  ssl: process.env.DB_SSL === 'true' ? {
    rejectUnauthorized: false
  } : false
});

async function seedUsers() {
  const client = await pool.connect();

  try {
    const usuarios = [
      { nome: 'jerusa', email: '<EMAIL>', senha: '123455' },
      { nome: 'cleiton', email: '<EMAIL>', senha: '123456' },
      { nome: 'maria', email: '<EMAIL>', senha: '123456' },
      { nome: 'pedro', email: '<EMAIL>', senha: '123456' },
      { nome: 'gabriel', email: '<EMAIL>', senha: '123456' }
    ];

    for (const usuario of usuarios) {
      await client.query(
        'INSERT INTO usuarios (nome, email, senha) VALUES ($1, $2, $3) ON CONFLICT (email) DO UPDATE SET nome = $1, senha = $3',
        [usuario.nome, usuario.email, usuario.senha]
      );
    }

    console.log('Usuários inseridos ou atualizados com sucesso!');
  } catch (err) {
    console.error('Erro ao inserir ou atualizar usuários:', err);
  } finally {
    client.release();
  }
}

module.exports = seedUsers;

// Não execute a função aqui, apenas exporte-a
