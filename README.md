# Inteli - Instituto de Tecnologia e Liderança

<p align="center">
<a href= "https://www.inteli.edu.br/"><img src="/assets/inteli.png" alt="Inteli - Instituto de Tecnologia e Liderança" border="0"></a>
</p>

# Nome do projeto

## CTRL + S

## Integrantes:
- <a href="https://www.linkedin.com/in/ana-cristina-jardim"><PERSON> </a>
- <a href="https://www.linkedin.com/">Cauã Pirilo Asquino</a>
- <a href="https://www.linkedin.com/in/eduardooliveiralucio"><PERSON></a>
- <a href="https://www.linkedin.com/in/enzo-piol-cerutti">En<PERSON></a>
- <a href="https://www.linkedin.com/in/leunam?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app">Leunam Sousa <PERSON></a>
- <a href="https://www.linkedin.com/in/lucas-michel-pereira-1a338734b/"><PERSON></a>
- <a href="https://www.linkedin.com/in/marianalacerdareis">Mariana Lacerda Reis</a>
- <a href="https://www.linkedin.com/in/pietro-alkmin?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app">Pietro Mota Alkmin</a>

## Professores:
### Orientador(a)
- <a href="https://www.linkedin.com/in/profclaudioandre/">Cláudio Fernando André</a>
### Instrutores
- <a href="https://www.linkedin.com/in/anacristinadossantos/">Ana Cristina dos Santos</a>
- <a href="https://github.com/brunamayer">Bruna Mayer Costa</a>
- <a href="https://www.linkedin.com/in/diogo-martins-gon%C3%A7alves-de-morais-96404732/">Diogo Martins Gonçalves de Morais</a>
- <a href="https://www.linkedin.com/in/henrique-mohallem-paiva-6854b460/">Henrique Mohallem Paiva</a>
- <a href="https://www.linkedin.com/in/kizzyterra/">Kizzy Terra</a>

## 📝 Descrição

Durante o segundo módulo do primeiro ano do Instituto de Tecnologia e Liderança, em colaboração com o Instituto de Pesquisas Tecnológicas (IPT), desenvolvemos o projeto Ctrl + S. Uma aplicação web que tem como objetivo principal facilitar e padronizar o processo de inspeções prediais.

Cada construção possui características únicas, como tipo estrutural, sistemas e estrutura, o que dificulta a criação de uma solução digital única e eficiente. Além disso, fatores como a ausência de padronização nas ferramentas existentes, a insegurança no armazenamento de dados e a desorganização dos registros manuais tornam o processo ainda mais desafiador para os engenheiros responsáveis pelas inspeções.

A plataforma que está sendo desenvolvida busca ser ao mesmo tempo intuitiva e flexível, o que permite sua adaptação às diferentes realidades dos edifícios. Para isso, a solução web foi projetada com foco na usabilidade e organização estruturada das informações, além de considerar a integração com plantas e documentos técnicos e o uso de pré-relatórios padronizados, visando aumentar a confiabilidade e eficiência do processo.

Com base nas necessidades identificadas junto aos profissionais da área, entregamos uma solução que agregua valor real às inspeções prediais, tornando-as mais práticas, rápidas e seguras. O projeto Ctrl + S tem como missão não apenas resolver os gargalos atuais do setor, mas também incentivar a adoção de tecnologias por meio de uma plataforma que prioriza simplicidade, segurança e clareza na apresentação das informações.

## 📝 Link de demonstração

_Coloque aqui o link para seu projeto publicado e link para vídeo de demonstração_



## 📁 Estrutura de pastas

```
2025-1B-T16-IN02-G03/
├── src/
│   ├── controllers/          # Lógica de negócio
│   ├── models/               # Modelos de dados
│   ├── routes/               # Definição de rotas
│   ├── views/                # Templates HTML
│   ├── assets/               # Recursos estáticos
│   └── migrations/           # Scripts de banco
├── documentos/               # Documentação
│   ├── wad.md
│   └── endpoints.md
├── assets/                   # Imagens do projeto
├── server.js                 # Servidor principal
├── package.json              # Dependências
└── README.md                 # Este arquivo
```

Dentre os arquivos e pastas presentes na raiz do projeto, definem-se:

- <b>src</b>: Todo o código fonte criado para o desenvolvimento do projeto de aplicação web.

- <b>documentos</b>: Aqui estão todos os documentos do projeto, como o Web Application Document (WAD) bem como documentos complementares.

- <b>assets</b>: Aqui estão os arquivos relacionados a elementos não-estruturados do Web Application Document (WAD), como imagens.

- <b>README.md</b>: Arquivo que serve como guia introdutório e explicação geral sobre o projeto e a aplicação (o mesmo arquivo que você está lendo agora).

## Configuração para desenvolvimento e execução do código

Aqui encontram-se todas as instruções necessárias para a instalação de todos os programas, bibliotecas e ferramentas imprescindíveis para a configuração do ambiente de desenvolvimento.

1. Baixar e instalar o node.js: [https://nodejs.org/pt-br/](https://nodejs.org/pt-br/) (versão 16.15.1 LTS)
2. Clone o repositório em questão.
3. No modo administrador, abra o "prompt de comando" ou o "terminal" e, após, abra a pasta raiz do repositório clonado e digite o comando:

```sh
npm install
```

Isso instalará todas as dependências definidas no arquivo <b>package.json</b> que são necessárias para rodar o projeto. Agora o projeto já está pronto para ser modificado. Caso ainda deseje iniciar a aplicação, digite o comando abaixo no terminal:

```sh
npm start
```
5. Agora você pode acessar a aplicação através do link http://localhost:3000/.

6. O servidor está online.

## 🗃 Histórico de lançamentos

* 0.5.0 - XX/XX/2025

* 0.4.0 - XX/XX/2025
    *
* 0.3.0 - 30/05/2025
    - Desenvolvimento do protótipo de alta fidelidade;
    - Implementação da arquitetura MVC (Model-View-Controller);
    - Criação dos primeiros models, controllers e views;
    - Desenvolvimento da primeira versão HTML e CSS básica;
    - Atualização do banco de dados com sugestões do parceiro.
* 0.2.0 - 16/05/2025
    - Criação dos wireframes de baixa fidelidade;
    - Desenvolvimento da primeira versão do banco de dados;
    - Entendimento e validação da estrutura do projeto;
    - Definição da arquitetura inicial do sistema.
* 0.1.0 - 30/04/2025
    - Entendimento do negócio: 5 Forças de Porter, análise Swot e descrição da solução;
    - Entendimento do usuário: Personas e User Stories.


## 📋 Licença/License

<img style="height:22px!important;margin-left:3px;vertical-align:text-bottom;" src="https://mirrors.creativecommons.org/presskit/icons/cc.svg?ref=chooser-v1">
<img style="height:22px!important;margin-left:3px;vertical-align:text-bottom;" src="https://mirrors.creativecommons.org/presskit/icons/by.svg?ref=chooser-v1">
<p xmlns:cc="http://creativecommons.org/ns#" xmlns:dct="http://purl.org/dc/terms/">
  <a property="dct:title" rel="cc:attributionURL" href="https://github.com/Intelihub/2025-1B-T16-IN02-G03/">CTRL + S</a> by
  Inteli, Ana Cristina Alves Jardim, Cauã Pirilo Asquino, Eduardo de Oliveira Lucio, Enzo Piol Cerutti, Leunam Sousa de Jesus, Lucas Michel Pereira, Mariana Lacerda Reis, Pietro Mota Alkmin </a>
  is licensed under
  <a href="http://creativecommons.org/licenses/by/4.0/?ref=chooser-v1" target="_blank" rel="license noopener noreferrer" style="display:inline-block;">Attribution 4.0 International</a>.
</p>

