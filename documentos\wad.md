<img src="../assets/logointeli.png">

# WAD - Web Application Document - Módulo 2 - Inteli

**_Os trechos em itálico servem apenas como guia para o preenchimento da seção. Por esse motivo, não devem fazer parte da documentação final_**

## **CTRL + S**

#### Nomes dos integrantes do grupo
- <a href="https://www.linkedin.com/in/ana-cristina-jardim"><PERSON> </a>
- <a href="https://www.linkedin.com/">Cauã Pirilo Asquino</a>
- <a href="https://www.linkedin.com/in/eduardooliveiralucio"><PERSON></a>
- <a href="https://www.linkedin.com/in/enzo-piol-cerutti">En<PERSON></a>
- <a href="https://www.linkedin.com/in/leunam?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app">Leunam <PERSON>usa <PERSON></a>
- <a href="https://www.linkedin.com/in/lucas-michel-pereira-1a338734b/"><PERSON></a>
- <a href="https://www.linkedin.com/in/marianalacerdareis">Mariana Lacerda Reis</a>
- <a href="https://www.linkedin.com/in/pietro-alkmin?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app">Pietro Mota Alkmin</a>

## Sumário

[1. Introdução](#c1)

[2. Visão Geral da Aplicação Web](#c2)

[3. Projeto Técnico da Aplicação Web](#c3)

[4. Desenvolvimento da Aplicação Web](#c4)

[5. Testes da Aplicação Web](#c5)

[6. Estudo de Mercado e Plano de Marketing](#c6)

[7. Conclusões e trabalhos futuros](#c7)

[8. Referências](#c8)

[Anexos](#c9)

<br>


# <a name="c1"></a>1. Introdução (sprints 1 a 5)

Nós, do grupo Crtl + S, identificamos junto ao Instituto de Pesquisas Tecnológicas (IPT) que um dos principais desafios nas inspeções prediais é a complexidade gerada pela diversidade entre os edifícios. Cada construção possui características únicas como idade, tipo estrutural, sistemas e equipamentos o que dificulta a criação de uma solução digital única que atenda a todos os casos. Além disso, a falta de padronização entre as ferramentas existentes, a preocupação com a segurança dos dados e a dificuldade de organizar manualmente os registros tornam o processo ainda mais desafiador.

Diante desse cenário, estamos desenvolvendo uma aplicação web que busca facilitar e padronizar as inspeções prediais. Nossa proposta é criar uma ferramenta intuitiva e flexível, capaz de se adaptar às diferentes realidades dos edifícios e auxiliar na coleta e organização de dados de forma estruturada. Também consideramos a possibilidade de integração com plantas e documentos técnicos, e o uso de pré-relatórios padronizados para tornar o processo mais eficiente e confiável.

Nosso foco está em entender a fundo as necessidades dos engenheiros responsáveis pelas inspeções prediais para entregar uma solução que realmente agregue valor e torne as inspeções mais práticas, rápidas e seguras. Ainda estamos na fase de desenvolvimento, mas temos como objetivo criar uma aplicação que não só resolva os principais gargalos atuais, como também incentive a adoção de tecnologias no setor por meio da simplicidade, da segurança e da clareza nas informações geradas.

# <a name="c2"></a>2. Visão Geral da Aplicação Web 

## 2.1. Escopo do Projeto (sprints 1 e 4)

### 2.1.1. Modelo de 5 Forças de Porter 

A fim de obter uma análise mais aprofundada do Instituto de Pesquisas Tecnológicas (IPT), utilizou-se o modelo das 5 Forças de Porter. Esse modelo permite compreender o ambiente competitivo em que uma organização está inserida, avaliando cinco forças distintas: o poder de barganha dos fornecedores, o poder de barganha dos clientes, a ameaça de produtos substitutos, a ameaça de novos entrantes e a rivalidade entre os concorrentes (PORTER, 1980)[¹](#porter). No caso do IPT, é possível analisar o impacto de cada uma dessas forças sobre sua atuação, classificando-as em níveis alto, médio ou baixo, conforme demonstrado a seguir:

<div align="center">
  <sub>FIGURA X - Modelo de 5 Forças de Porter </sub><br>
  <img src= "../assets/wad/5forcasDePorter.png" width="100%"
  alt="5 Forças de Porter"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

**- Poder de Barganha dos fornecedores: Alto**</br>
O IPT possui uma infraestrutura de ponta e utiliza equipamentos altamente tecnológicos, com elevado valor agregado. Dessa forma, os fornecedores desses materiais e serviços exercem um poder de barganha significativo, sendo capazes de influenciar os custos operacionais do instituto, como evidenciado pelas licitações e contratos disponíveis no site oficial. (IPT, [s.d.])[²](#ipt).

**- Poder de Barganha dos Clientes: Médio**</br>
O instituto oferece soluções tecnológicas específicas e inovadoras, com poucos concorrentes capazes de reproduzi-las. Nesses casos, o poder de barganha dos clientes é reduzido. No entanto, para serviços como monitoramento, inspeção e consultoria, existem mais empresas capacitadas a fornecer esses serviços, aumentando o poder de barganha dos clientes. Portanto, pela média, esse poder pode ser considerado moderado.

**- Ameaça de Produtos Substitutos: Média**</br>
Para as soluções tecnológicas desenvolvidas pelo IPT, a ameaça de produtos substitutos é considerada baixa, em razão da especificidade e do caráter inovador desses produtos. Por outro lado, no caso de serviços como consultoria e inspeção, a concorrência é mais acirrada, com diversas empresas oferecendo alternativas semelhantes, o que eleva a possibilidade de substituição. Assim, ao considerar ambos os aspectos, classifica-se essa força como de ameaça média.

**- Ameaça de Novos Entrantes: Baixa**</br>
 O mercado de pesquisa e desenvolvimento tecnológico exige investimentos significativos em infraestrutura e conhecimento especializado. Adicionalmente, o IPT oferece serviços de consultoria técnica e vistoria especializada para a identificação e resolução de falhas estruturais, operacionais ou ambientais em empresas e obras públicas ou privadas. A complexidade técnica desses serviços representa uma barreira significativa à entrada de novos concorrentes.

**- Rivalidade entre os concorrentes: Média**</br>
 A rivalidade varia de acordo com os tipos de serviços prestados. Em desenvolvimento tecnológico e inovação, o número de instituições com infraestrutura e conhecimento comparáveis ao do IPT é reduzido, o que diminui a pressão competitiva. No entanto, em serviços como consultoria técnica, inspeções e análises laboratoriais, há uma concorrência maior. Empresas privadas, universidades com núcleos tecnológicos e outros institutos de pesquisa competem por contratos e projetos.

 Em suma, a análise das 5 Forças de Porter aplicada ao Instituto de Pesquisas Tecnológicas (IPT) evidencia a complexidade e a especificidade do setor em que a instituição está inserida. Observa-se uma concorrência mais acirrada em serviços com menor barreira tecnológica, como consultorias e inspeções, o que exige constante atualização e diferenciação. Ainda assim, o prestígio histórico da instituição, aliado ao seu vínculo com o Estado e à excelência técnica, posiciona o IPT como um player relevante e resiliente no ecossistema de pesquisa e desenvolvimento no Brasil.

### 2.1.2. Análise SWOT da Instituição Parceira 

A seguir, é apresentada a Matriz SWOT com a análise do Instituto de Pesquisas Tecnológicas (IPT).

A Análise SWOT é uma ferramenta amplamente utilizada para avaliar o posicionamento estratégico de instituições frente ao ambiente interno e externo. No caso do IPT, essa análise permite identificar suas forças e fraquezas internas, bem como as oportunidades e ameaças presentes no contexto em que está inserido. Ao aplicar essa metodologia, é possível compreender com maior clareza os desafios enfrentados pelo instituto, suas vantagens competitivas e os caminhos mais promissores para seu desenvolvimento.

Quando bem elaborada, a matriz SWOT oferece um diagnóstico útil para orientar decisões estratégicas, contribuindo para o fortalecimento institucional do IPT e para o planejamento de ações mais eficazes no médio e longo prazo, especialmente diante da constante evolução tecnológica e das mudanças nas políticas públicas e demandas do mercado.

<div align="center">
  <sub>FIGURA X - Análise SWOT</sub><br>
  <img src= "../assets/wad/swot.png" width="100%"
  alt=" Análise SWOT "><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

### 2.1.3. Solução (sprints 1 a 5)

**1. Problema a ser resolvido**

   Notou-se o problema da grande quantidade de dados e fotos que precisam estar alinhados aos projetos das edificações e, posteriormente, registrados em relatório. Isso compromete a agilidade, a confiabilidade e a efetividade das inspeções prediais.

**2. Dados disponíveis**

   Não se aplica.

**3. Solução proposta**

   A solução contempla uma Aplicação *Web* que permitirá o registro de dados (textos e imagens) estruturados da inspeção predial, que poderão ser exportados com uma breve descrição para auxiliar o profissional com a escrita do relatório.

**4. Forma de utilização da solução**

   A solução será implementada em todo o processo da inspeção predial e análise de seus sistemas, que são realizadas pelo Instituto de Pesquisas Tecnológicas do Estado de São Paulo - IPT. Essa aplicação terá uma interface amigável e intuitiva, garantindo que os funcionários consigam utilizá-la com facilidade. Além disso, a solução proposta vai garantir o fluxo de trabalho típico de uma inspeção predial, contando com recursos para registrar informações, anotações relevantes e capturar imagens.

**5. Benefícios esperados**

   A Aplicação *Web* tem o objetivo de trazer diversos benefícios significativos para o processo de inspeção predial. Ao permitir o registro de dados, como texto e imagens, ela facilita e organiza as informações coletadas durante a inspeção, o que torna o processo mais ágil e eficiente. Além disso, a possibilidade de exportar esses dados com descrições automáticas auxilia o profissional na escrita do relatório final, o que reduz o tempo gasto com processos repetitivos e garante o aumento da produtividade.

**6. Critério de sucesso e como será avaliado**

   O critério de sucesso estará relacionado à efetividade da aplicação em tornar o processo de inspeção predial mais ágil, organizado e preciso. Para isso, serão considerados fatores como a redução do tempo necessário para a elaboração dos relatórios, a satisfação dos usuários com a ferramenta, a diminuição de erros e retrabalhos, além da qualidade dos feedbacks fornecidos pelos clientes.
 
### 2.1.4. Value Proposition Canvas 
O Canvas de Proposta de Valor é uma ferramenta visual que ajuda a descrever como produtos ou serviços criam valor para os clientes. Ele permite alinhar a oferta da empresa com as necessidades, dores e desejos do público-alvo, por meio da análise do perfil do cliente e do mapa de valor. Essa estrutura facilita a criação de soluções mais eficazes e centradas no usuário (OSTERWALDER; PIGNEUR, 2014).[³](#osterwalder)

<div align="center">
  <sub>FIGURA Y - Canvas Proposta de Valor</sub><br>
  <img src= "../assets/wad/canvasPropostaDeValor.png" width="100%"
  alt="Canvas Proposta de Valor"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

Atualmente, o processo de fiscalização de inspeções prediais é manual e desorganizado, dificultando o trabalho dos engenheiros e aumentando o tempo necessário para documentação e geração de relatórios. Esse cenário pode gerar inconsistências nas inspeções e impacto na qualidade técnica dos laudos.

Para solucionar essa dor, propomos ao IPT uma aplicação web que transforma o registro de inspeções prediais em um processo estruturado e eficiente. Diferente de métodos convencionais, que muitas vezes são fragmentados e pouco sistemáticos, nossa aplicação permite o registro integrado de dados e imagens, organizando as informações em banco de dados estruturado e gerando relatórios técnicos automaticamente.

Os benefícios esperados incluem maior produtividade nas inspeções, com redução do tempo necessário para documentação, padronização dos relatórios técnicos por meio de templates consistentes e melhor organização dos dados coletados, tornando os engenheiros mais eficientes e precisos em suas análises e recomendações técnicas.

### 2.1.5. Matriz de Riscos do Projeto

A ferramenta **Matriz de Risco** propõe entender as probabilidades de um evento acontecer e o impacto que isso pode gerar no projeto, evitando ou revertendo determinados riscos. É importante ressaltar que o número de níveis definidos para a probabilidade deve ser o mesmo do impacto. Por exemplo: se houver 5 níveis de probabilidade (10%, 30%, 50%, 70% e 90%), devem existir também 5 níveis de impacto (muito baixo, baixo, médio, alto e muito alto).(ISO31000.NET, 2022)[⁴](#iso).

As categorias de risco são tradicionalmente divididas por cores:

- **Verde**: Problemas de baixo risco, classificados por baixa probabilidade ou impacto reduzido.
- **Amarelo**: Problemas de risco médio, que exigem atenção e monitoramento constante.
- **Vermelho**: Problemas de alto risco, que requerem ação imediata devido à sua alta probabilidade e/ou impacto severo.

<div align="center">
  <sub>FIGURA X - Matriz de Risco</sub><br>
  <img src= "../assets/wad/matrizDeRisco.png" width="100%"
  alt="Matriz de Risco"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

## Riscos Baixos

### Interface pouco intuitiva
- **Descrição:** A interface pode não ser suficientemente intuitiva para os inspetores, dificultando a adoção da ferramenta.
- **Oportunidade:** Ao adaptar a interface com base em quem usa, aumentam as chances de adoção da ferramenta e aprendizado prático da equipe em UX/UI.
- **Probabilidade:** 50%
- **Impacto:** 15%

### Preocupações iniciais com a segurança dos dados
- **Descrição:** Insegurança dos usuários em relação à proteção dos dados, especialmente no início do uso.
- **Oportunidade:** Ao implementar boas práticas de segurança e comunicação transparente sobre proteção de dados a confiança na solução aumenta, além do aprendizado para a equipe.
- **Probabilidade:** 20%
- **Impacto:** 30%

### Dificuldade inicial na exportação de relatórios
- **Descrição:** Problemas ao exportar dados em formatos .txt ou .csv.
- **Oportunidade:** Gerando relatórios automáticos e profissionais em estrutura de dados e automação de processos o sistema se torna mais otimizado e a equipe adquiri experiência em uma área mais avançada.
- **Probabilidade:** 20%
- **Impacto:** 15%

---

## Riscos Médios

### Múltiplos usos simultâneos gerando instabilidade no sistema
- **Descrição:** O uso simultâneo por vários inspetores ou gestores pode sobrecarregar o sistema, provocando lentidão, falhas de carregamento ou até quedas temporárias.
- **Oportunidade:** A aplicação se torna mais preparada para o uso institucional e a equipe adquiri aprendizado técnico avançado em infraestrutura escalável.
- **Probabilidade:** 20%
- **Impacto:** 45%

### Falhas na responsividade para dispositivos móveis
- **Descrição:** A experiência de uso pode ser prejudicada em smartphones ou tablets.
- **Oportunidade:** Responsividade da aplicação e aprendizado da equipe sobre desenvolvimento mobile-first e front-end adaptável.
- **Probabilidade:** 50%
- **Impacto:** 30%

### Dificuldade de integração com sistema interno do IPT.
- **Descrição:** Durante a integração com o IPT podem surgir incompatibilidades de formatos de dados, campos obrigatórios ou protocolos de comunicação.
- **Oportunidade:** Conexão real com o ambiente institucional e experiência prática da equipe em integração de sistemas e APIs.
- **Probabilidade:** 80%
- **Impacto:** 15%

---

## Riscos Altos

### Falhas técnicas críticas na coleta de dados
- **Descrição:** Perda ou corrupção de dados durante o registro de textos e imagens durante inspeções.
- **Oportunidade:** Fortificação do sistema e aprendizado da equipe sobre estratégias de prevenção de falhas.
- **Probabilidade:** 50%
- **Impacto:** 45%

### Atraso no desenvolvimento do MVP
- **Descrição:** Possibilidade de atrasos no cronograma de desenvolvimento, impactando entregas para o IPT.
- **Oportunidade:** Aprendizado em gestão de projetos ágeis, melhora da execução e planejamento da equipe.
- **Probabilidade:** 80%
- **Impacto:** 45%

### Dificuldade de adaptação a diferentes tipos de edifícios.
- **Descrição:** A flexibilidade prevista pode não atender todas as variações estruturais e de sistemas dos edifícios.
- **Oportunidade:** Escalabilidade futura da aplicação e desenvolvimento de pensamento modular da equipe.
- **Probabilidade:** 80%
- **Impacto:** 30%

## 2.2. Personas 

Personas são representações semifictícias de segmentos‑chave de usuários, criadas a partir de dados demográficos, comportamentais e psicográficos coletados por meio de pesquisas de campo, entrevistas e análises de mercado; ao sintetizar motivações, frustrações e objetivos recorrentes em narrativas tangíveis, elas ajudam equipes de design e produto a tomar decisões mais empáticas e fundamentadas, o que, segundo pesquisa do MIT Integrated Design & Management Program, pode elevar em até 20 % a satisfação do usuário final e reduzir em 15 % o retrabalho durante a prototipagem (LEE; FLEMING, 2023).[⁵](#lee)

<div align="center">
  <sub>FIGURA X - Persona 1</sub>
  <img src= "../assets/wad/Persona1.png" width="100%" 
  alt="Persona 1- Roberto">
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>
Roberto O. Andrade é um coordenador experiente que atua como elo técnico entre os inspetores de campo e a gestão superior. Seu perfil é metódico, exigente com a qualidade dos registros e comprometido com a credibilidade institucional. Prefere se comunicar por e-mail, Teams ou LinkedIn, utilizando o WhatsApp apenas em situações de emergência, o que reflete sua preferência por canais formais e organizados. Além de orientar tecnicamente a equipe, ele também assume um papel de mentor, garantindo que os dados estejam padronizados e prontos para a análise da administração.

<a id="LaisMonteiro"></a> <div align="center">
  <sub>FIGURA X - Persona 2</sub>
  <img src= "../assets/wad/Persona2.png" width="100%" 
  alt="Persona 2- Laís">
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>
Laís F. Monteiro tem perfil prático e altamente adaptável a tecnologias, usando com frequência o WhatsApp para comunicação em campo e plataformas como Google Drive e YouTube para consultas rápidas. Como inspetora, representa a linha de frente das auditorias e depende de ferramentas que funcionem mesmo sem conexão, dada a realidade de locais com baixa cobertura. É intolerante a processos redundantes e a registros manuais que exijam retrabalho, por isso busca soluções que integrem dados de forma ágil e segura.

<div align="center">
  <sub>FIGURA X - Persona 3</sub><br>
  <img src= "../assets/wad/Persona3.png" width="100%"
  alt="Persona 3- Carlos"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>
Carlos H. Batista gerencia diversas frentes de obra e precisa de informações precisas e integradas para tomar decisões rápidas e justificar tecnicamente suas escolhas diante de órgãos fiscalizadores. Utiliza ferramentas como Trello, Sienge, Project, além do Teams e WhatsApp para manter o controle operacional e de comunicação. Frustra-se com a descentralização de dados e depende de relatórios padronizados para garantir fluidez na gestão e segurança nas auditorias.



## 2.3. User Stories (sprints 1 a 5)

Esta seção apresenta todas as User Stories identificadas para o projeto. As User Stories nada mais são do que descrições curtas e objetivas que representam funcionalidades do sistema sob a perspectiva do usuário final. Elas são elaboradas com base nas personas definidas e seguem a estrutura: "**Como** [tipo de usuário], **eu quero** [ação], **para que eu** [benefício ou objetivo]". Assim, é possível garantir que cada funcionalidade seja pensada a partir das necessidades reais do usuário — considerando quem ele é, o que busca e de que forma a funcionalidade trará valor à sua experiência (COHN, 2004)[⁶](#cohn).

Além disso, todas as User Stories foram criadas seguindo os critérios do modelo INVEST, um acrônimo que orienta boas práticas para a formulação de histórias de usuário e siginifica:

- I – Independente;

- N – Negociável;

- V – Valiosa;

- E – Estimável;

- S – Small (Pequena);

- T – Testável.

Vale destacar que, para as cinco primeiras User Stories, consideradas prioritárias, foi incluída uma análise explicativa de como cada uma atende aos critérios do INVEST.

<a id="US01"></a> Identificação | US01
--- | ---
Persona | Laís F. Monteiro
User Story | "Como inspetora da residência, posso relatar um problema identificado na inspeção por meio de uma breve descrição para que ele possa ser facilmente entendido durante a criação do relatório final"
Critério de aceite 1 | CR1: O usuário sem a permissão adequada não deve ser capaz de criar a instanciação de um problema.
Critério de aceite 2 | CR2: O usuário com a permissão adequada deve ser capaz de criar a instanciação de um problema identificado na inspeção.
Critério de aceite 3 | CR3: O usuário com a permissão adequada deve ser capaz de nomear o problema encontrado.
Critério de aceite 4 | CR4: O usuário com a permissão adequada deve ser capaz de anexar uma breve descrição do problema na instanciação.
Critérios INVEST | É **independente** pois descreve uma funcionalidade específica (relatar um problema identificado) que pode ser desenvolvida e testada de forma isolada, sem depender de outras. É **negociável** pois o objetivo principal (registrar o problema de forma clara) pode ser alcançado de diferentes maneiras, podendo ser discutidas posteriormente. É **valiosa** pois permite que o trabalho da inspetora seja documentado de forma eficaz, garantindo clareza no relatório final e melhorando a comunicação entre as formas de registro. É **estimável** pois sabemos que envolve a criação de um formulário com nome e descrição do problema, além de controle de permissões. É **sob medida** pois considera apenas a funcionalidade de criar, nomear e descrever um problema identificado, abordando o suficiente para o uso básico da ferramenta mas sem considerar todas as funcionalidades possíveis. É **testável** pois os critérios de aceite estão bem definidos, verificando a capacidade de criar um problema na aplicação, nomeá-lo e descrevê-lo quando o usuário tem a permissão correta.

---

<a id="US02"></a> Identificação | US02
--- | ---
Persona | Laís F. Monteiro
User Story | "Como inspetora da residência, posso adicionar fotos para cada problema identificado na inspeção para conectar mais facilmente o problema à imagem (que será usada como evidência)"
Critério de aceite 1 | CR1: O usuário deve ser capaz de anexar fotos de seu dispositivo na instanciação de problema.
Critério de aceite 2 | CR2: O usuário deve ser capaz de visualizar a foto anexada de modo fácil e intuitivo.
Critério de aceite 3 | CR3: A imagem deve estar claramente conectada ao problema correspondente a ela.
Critérios INVEST | É **independente** pois trata exclusivamente da associação de imagens aos problemas identificados, não dependendo de outras histórias para seu entendimento. É **negociável** pois os formatos de arquivos aceitos e outros aspectos podem ser discutidos posteriormente. É **valiosa** pois agregar imagens ao problema aumenta a velocidade de criação do relatório final e reduz o risco de erros. É **estimável** pois as informações fornecidas são suficientes para entender tudo que deve ser produzido, possibilitando uma estimativa de tempo confiável. É **sob medida** pois considera apenas funcionalidades relacionadas à adição de imagens para cada problema, não incluindo outras funcionalidades extras. É **testável** pois os critérios de aceite sugerem intuitivamente o que deve ser testado e como.

---

Identificação | US03
--- | ---
Persona | Roberto O. Andrade
User Story | "Como inspetor/coordenador, quero registrar novas inspeções em novas habitações, preenchendo informações específicas de cada caso, para garantir que os dados estejam organizados e acessíveis de forma clara"
Critério de aceite 1 | CR1: O usuário, com a permissão adequada, deve ser capaz de criar uma nova instância de habitação.
Critério de aceite 2 | CR2: O usuário, com a permissão adequada, deve ser capaz de atribuir nome, endereço e outras informações específicas à habitação criada.
Critério de aceite 3 | CR3: O usuário, com a permissão adequada, deve ser capaz de criar uma nova instância de inspeção dentro de qualquer habitação existente.
Critério de aceite 4 | CR4: O usuário, com a permissão adequada, deve ser capaz de atribuir nome, data e outras informações específicas à inspeção criada.
Critérios INVEST | É **independente** pois trata especificamente da funcionalidade de registro de inspeções em habitações, podendo ser desenvolvida e testada sem depender diretamente de outras funcionalidades do sistema. É **negociável** pois as opções para preenchimento das informações específicas (por exemplo, campos adicionais ou formatos) podem ser discutidas entre os stakeholders para melhor atender às necessidades do usuário. É **valiosa** pois permite que o inspetor registre e organize as inspeções de maneira clara, garantindo que as informações sejam acessíveis e facilmente encontradas quando necessário. É **estimável** pois a funcionalidade de criação e preenchimento de informações é um processo bem compreendido, o que permite uma estimativa razoável de tempo e esforço para sua implementação. É **sob medida** pois é restrita à criação de habitações e inspeções, sem envolver outras partes do sistema, como a análise dos dados ou interações com outros tipos de usuário. É **testável** pois é possível criar cenários de teste para registrar habitações e inspeções, validando se os campos de entrada são preenchidos corretamente e se as informações são salvas adequadamente no sistema.

---

Identificação | US04
--- | ---
Persona | Carlos H. Batista
User Story | "Como administrador de inspeções, posso atribuir inspeções a inspetores específicos para que haja melhor organização de tarefas e eles possam adicionar os problemas correspondentes"
Critério de aceite 1 | CR1: O usuário, com a permissão adequada, deve ser capaz de selecionar um ou mais de seus inspetores para uma inspeção existente (e durante sua criação).
Critério de aceite 2 | CR2: O usuário, com a permissão adequada, deve ser capaz de facilmente visualizar quais inspetores foram atribuídos a cada inspeção.
Critério de aceite 3 | CR3: O usuário, como inspetor selecionado, deve ser capaz de acessar a inspeção e alterar o permitido.
Critério de aceite 4 | CR4: O usuário, como inspetor não selecionado, não deve ser capaz de visualizar, acessar nem alterar a inspeção.
Critérios INVEST | É **independente** pois trata especificamente da funcionalidade de atribuição de inspeções a inspetores, podendo ser desenvolvida e testada separadamente de outras funcionalidades, como o preenchimento dos relatórios ou geração de alertas. É **negociável** pois a forma como a atribuição é feita (manual, por filtros, com sugestões automáticas, etc.) pode ser discutida entre os envolvidos no projeto para melhor atender às necessidades da equipe. É **valiosa** pois garante uma organização eficiente das tarefas, possibilita o controle adequado de quem pode acessar e editar cada inspeção e contribui diretamente para a segurança e integridade das informações. É **estimável** pois a implementação da lógica de atribuição e controle de acesso é uma prática comum e bem documentada, permitindo estimativas realistas de tempo e esforço. É **sob medida** pois é limitada à funcionalidade de distribuição de inspeções entre inspetores, sem interferir diretamente em outras áreas do sistema. É **testável** pois é possível criar usuários com diferentes níveis de permissão e simular a atribuição e tentativa de acesso, verificando se o sistema se comporta conforme o esperado.

---

Identificação | US05
--- | ---
Persona | Roberto O. Andrade
User Story | "Como usuário da aplicação, posso fazer login na minha conta usando meu e-mail para acessar as inspeções passadas e atuais específicas a mim e receber crédito pelas mudanças feitas por mim"
Critério de aceite 1 | CR1: O usuário deve ser capaz de possuir uma conta própria com suas próprias informações e acessos individuais.
Critério de aceite 2 | CR2: O usuário deve ser capaz de anexar seu e-mail à conta como modo de acesso.
Critério de aceite 3 | CR3: O usuário deve ser capaz de atribuir uma senha de sua escolha à sua conta.
Critério de aceite 4 | CR4: O usuário deve ser capaz de acessar sua conta quando desejar usando seu e-mail e senha.
Critérios INVEST | É **independente** pois trata especificamente da funcionalidade de autenticação, podendo ser desenvolvida e testada sem depender diretamente de outras funcionalidades. É **negociável** pois os métodos de autenticação podem ser discutidos, podendo incluir autenticação em dois fatores e outros métodos muito utilizados. É **valiosa** pois garante segurança, personalização e rastreabilidade nas ações dentro do sistema, também servindo para associar contribuições a usuários específicos. É **estimável** pois as funcionalidades de login e senha são muito bem conhecidas, permitindo uma fácil estimativa de tempo para sua implementação. É **sob medida** pois é restringida apenas ao processo de login do usuário. É **testável** pois é possível criar contas teste que seriam facilmente capazes de propiciar todos os testes necessários.

---

Identificação | US06
--- | ---
Persona | Roberto O. Andrade
User Story | "Como usuário da aplicação, posso copiar e/ou baixar as imagens de cada problema para sua fácil inserção no relatório final"
Critério de aceite 1 | CR1: O usuário, com a permissão adequada, deve ser capaz de acessar a foto original (e a desenhada) e copiá-la facilmente e intuitivamente.
Critério de aceite 2 | CR2: O usuário, com a permissão adequada, deve ser capaz de baixar a foto original (e a desenhada) para seu dispositivo.

---
Identificação | US07
--- | ---
Persona | Roberto O. Andrade
User Story | "Como inspetor/coordenador da residência, posso fazer indicações visuais nas imagens para destacar o exato local do problema encontrado"
Critério de aceite 1 | CR1: O usuário deve ser capaz de selecionar a imagem para desenhar nela a qualquer momento.
Critério de aceite 2 | CR2: O usuário deve ser instruído à possibilidade de desenhar na imagem selecionada (e no momento de sua adição).
Critério de aceite 3 | CR3: O usuário deve ser capaz de adicionar e remover linhas coloridas por cima da imagem selecionada a seu próprio critério.
Critério de aceite 4 | CR4: O usuário não deve ser capaz de alterar a imagem original com seus desenhos, apenas uma versão alternativa.

---

Identificação | US08
--- | ---
Persona | Roberto O. Andrade
User Story | "Como inspetor/coordenador da residência, posso categorizar cada problema de acordo com cômodo, natureza e severidade para entender todos os aspectos do problema mais facilmente"
Critério de aceite 1 | CR1: O usuário deve ser capaz de atribuir categorias para o problema selecionado (e durante a sua criação).
Critério de aceite 2 | CR2: O usuário deve ser capaz de remover ou alterar categorias antes atribuídas para o problema selecionado.
Critério de aceite 3 | CR3: O usuário deve ser capaz de ver as categorias de cada problema sem grande esforço (preferencialmente com diferenciação de cores, principalmente a severidade).

---

Identificação | US09
--- | ---
Persona | Roberto O. Andrade
User Story | "Como usuário da aplicação, posso acessar qualquer inspeção da qual eu seja participante ou pela qual eu seja responsável a qualquer momento para melhor organização e possibilitar o acompanhamento de múltiplas ao mesmo tempo"
Critério de aceite 1 | CR1: O usuário deve ter acesso a uma tela inicial contendo todas as residências inspecionadas que o usuário participou ou é responsável.
Critério de aceite 2 | CR2: O usuário deve ser capaz de selecionar a residência que desejar.
Critério de aceite 3 | CR3: O usuário, após selecionar uma residência, deve ter acesso a uma tela contendo todas as inspeções da residência selecionada que o usuário participou ou é responsável.
Critério de aceite 4 | CR4: O usuário deve ser capaz de selecionar a inspeção que desejar.

---

Identificação | US10
--- | ---
Persona | Laís F. Monteiro
User Story | "Como usuária da aplicação, posso filtrar os problemas de uma inspeção específica com base em suas categorias para encontrar o que preciso rapidamente"
Critério de aceite 1 | CR1: O usuário deve ser capaz de ativar e desativar filtros quando desejar.
Critério de aceite 2 | CR2: O usuário deve ser capaz de selecionar os filtros desejados.
Critério de aceite 3 | CR3: O usuário deve ser capaz de selecionar diversos filtros ao mesmo tempo.
Critério de aceite 4 | CR4: O usuário deve ser desencorajado a selecionar problemas que não correspondem aos filtros selecionados (seja por ocultá-los completamente ou por deixá-los mais transparentes).

---

Identificação | US11
--- | ---
Persona | Laís F. Monteiro
User Story | "Como usuária da aplicação, posso ordenar as inspeções expostas de acordo com o critério desejado para maior facilidade de navegação e identificação das inspeções levando em conta as necessidades do momento"
Critério de aceite 1 | CR1: O usuário deve ser fornecido com uma opção de ordenação que determina a ordem da exposição das inspeções.
Critério de aceite 2 | CR2: O usuário deve ser capaz de modificar a opção de modo a encaixá-la às suas necessidades.
Critério de aceite 3 | CR3: Dentre as opções fornecidas, deve haver: Data de criação, Progresso (se está ocorrendo atualmente, em processo de análise ou já foi concluída), Alfabética, Número de problemas, etc.
Critério de aceite 4 | CR4: O usuário deve ser capaz de inverter a ordem de uma opção específica clicando duas vezes no botão (exemplo: Ordem de data de criação começa pelas mais velhas no lugar das mais novas).

---

Identificação | US12
--- | ---
Persona | Laís F. Monteiro
User Story | "Como usuária da aplicação, posso organizar as inspeções que tenho acesso com seções criadas por mim mesma, para que meu fluxo de trabalho seja o mais eficiente possível para meu próprio estilo"
Critério de aceite 1 | CR1: O usuário deve ser, primeiramente, apresentado um modo de organização predefinido.
Critério de aceite 2 | CR2: O usuário deve ser capaz de criar novas seções de organização conforme o desejado (exemplo: pode criar um grupo de habitações que ficam perto da USP, ou um grupo de inspeções feitas no mês de março).
Critério de aceite 3 | CR3: O usuário deve ser capaz de nomear, descrever e inserir imagem para cada seção.
Critério de aceite 4 | CR4: O usuário deve ser capaz de remover seções de organização existentes (sem perder acesso às inspeções e outras seções dentro desta seção).


# <a name="c3"></a>3. Projeto da Aplicação Web (sprints 1 a 4)

## 3.1. Arquitetura (sprints 3 e 4)

O padrão arquitetural **MVC (Model-View-Controller)** é uma forma de estruturar aplicações que promove a separação de responsabilidades entre a interface com o usuário, a lógica de negócio e o gerenciamento de dados. Essa abordagem facilita a manutenção e a escalabilidade do sistema, tornando o desenvolvimento mais organizado e eficiente.

<div align="center">
  <sub>Diagrama MVC</sub><br>
  <img src= "../assets/wad/Diagrama MVC.png" width="100%" 
  alt="Diagrama MVC"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

No contexto deste sistema, o diagrama MVC representa de forma clara como os diferentes componentes da aplicação se relacionam:

- **View (verde)**: é a camada responsável por apresentar as informações ao usuário final, normalmente por meio de navegadores web utilizando tecnologias como HTML, CSS e JavaScript. Essa camada envia e recebe dados do Controller, mas não processa lógica de negócio diretamente.

- **Controller (azul)**: funciona como um intermediário entre a View e os Models. Ele interpreta as ações do usuário, executa as regras de negócio apropriadas e atualiza a View com base nos dados retornados pelos Models.

- **Model (vermelho)**: é responsável pela lógica de acesso e manipulação dos dados. Essa camada interage diretamente com o banco de dados, armazenando e recuperando as informações necessárias para o funcionamento do sistema.

Como podemos ver no diagrama, o backend da aplicação é estruturado com controllers e models distintos para cada entidade do sistema, como **Usuários**, **Projetos**, **Edifícios**, **Relatórios**, entre outros. Cada controller é encarregado de lidar com as operações relacionadas à respectiva entidade, enquanto os models definem a estrutura dos dados. O banco de dados utilizado é o **PostgreSQL**, com acesso sendo gerenciado através de uma aplicação em **Node.js**.

### Exemplos de Tabelas no Contexto do Diagrama MVC

Para entender melhor como funciona a interação entre as camadas do padrão MVC, podemos observar duas entidades do sistema: **Usuários** e **Relatórios**.

#### 1. Usuários

##### Model (usuarios)

A tabela usuarios armazena informações essenciais sobre os usuários da aplicação, como:

- id: identificador único do usuário.  
- nome, email, senha: dados de identificação e autenticação.

Esses dados são fundamentais para o controle de acesso ao sistema, gerenciamento de permissões e personalização da experiência do usuário.

##### Controller (usuariosController)

O controller usuariosController é responsável por lidar com todas as operações relacionadas à entidade usuarios, como:

- todosUsuarios(): retorna todos os usuários cadastrados.  
- buscarUsuarioPorId(): busca um usuário específico pelo ID.  
- criarUsuario(): cria um novo registro de usuário.  
- atualizarUsuario(): edita dados de um usuário existente.  
- deletarUsuario(): exclui um usuário do sistema.

Essas operações permitem que a aplicação mantenha a base de usuários atualizada e segura.

#### 2. Relatórios

##### Model (relatorio)

A tabela relatorio contém registros de avaliações ou inspeções realizadas no sistema, com campos como:

- imagem_url: link da imagem associada ao relatório.  
- avaliado_por: referência ao usuário responsável pela avaliação.  
- status: estado atual do relatório (ex: pendente, aprovado).  
- data_avaliacao, observacoes: informações adicionais sobre o processo de avaliação.

Esses dados são importantes para o acompanhamento e histórico das inspeções prediais.

##### Controller (relatorioController)

O controller relatorioController trata das requisições que envolvem os relatórios:

- todosRelatorios(): retorna todos os relatórios cadastrados.  
- buscarRelatorioPorId(): busca um relatório específico.  
- criarRelatorio(): adiciona um novo relatório ao banco de dados.  
- atualizarRelatorio(): edita um relatório existente.  
- deletarRelatorio(): remove um relatório da base.

Esse controller garante que o fluxo de informações sobre inspeções e avaliações esteja sempre em dia.

## 3.2. Wireframes
Wireframe é uma representação visual simplificada das interfaces de um sistema, amplamente empregada em projetos de UX/UI como etapa inicial do design. Seu principal objetivo é estruturar os elementos funcionais de uma página ou aplicação, como menus, botões e áreas de conteúdo, sem preocupação estética ou de estilo. Trata-se de um recurso de baixa fidelidade que facilita a comunicação entre equipes técnicas e stakeholders, permitindo o alinhamento prévio das funcionalidades antes do desenvolvimento (GARRETT, 2011)[⁷](#wireframes). 

No contexto deste projeto, o wireframe desenvolvido teve como finalidade mapear a jornada do usuário, segmentando-a em fluxos distintos para cada perfil envolvido. O wireframe a seguir descreve o fluxo de interface previsto para o perfil de administrador nesta aplicação web.

### Wireframe da tela na visão de administrador:
<div align="center">
  <sub>Wireframe de administrador</sub><br>
  <img src= "../assets/wad/wireframeAdm.png" width="100%"
  alt="Wireframe login de adm"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

Para uma visualização mais detalhada, o wireframe também está disponível na plataforma Figma, acessível por meio do seguinte link: [Wireframes](https://www.figma.com/design/l96rnt77hzGwxLGk4mBpzO/Wireframes-Crtl-S?node-id=0-1&t=yvIYowEMqkph3kYD-1).

Para facilitar a compreensão, as telas serão referenciadas neste documento pelos números atribuídos a elas no wireframe.

O sistema se inicia na tela 1, onde o administrador deve inserir seu e-mail e senha para acessar a plataforma. Caso ainda não esteja cadastrado, poderá realizar o cadastro por meio da tela 1.1.

Após o login, o administrador é direcionado à tela 2, que apresenta uma mensagem de boas-vindas e uma seção com inspeções acessadas recentemente. Nessa tela, há um botão que permite visualizar todas as inspeções cadastradas. À esquerda, encontra-se um menu lateral fixo com as seguintes opções: Início, Inspeções e Inspetores.

Ao clicar em "ver todas as inspeções" ou na opção Inspeções do menu, o administrador acessa a tela 3.2, que exibe uma lista filtrável por status: não iniciadas, em andamento ou concluídas. No topo da tela, há um botão “+” para criar uma nova inspeção.

Ao passar o cursor sobre uma inspeção listada, é possível visualizar detalhes como título, responsáveis, descrição e outras informações relevantes. Clicar em uma inspeção, seja na tela 3 ou na tela 3.2, leva o usuário à tela 3.2.2, onde são exibidas as patologias encontradas, com imagens e descrições. Também são apresentados dados como ambiente, sistema e data/hora. Nessa tela, há a opção de gerar um pré-relatório da inspeção e visualizar mais informações sobre cada patologia. Ao clicar em uma patologia específica, o administrador é redirecionado para a tela 3.2.3, que detalha suas características.

Ao clicar no botão “+” nas telas 3 ou 3.2, o administrador acessa a tela 3.2.1, onde pode criar uma nova inspeção preenchendo campos como título, descrição, seleção de integrantes e inclusão de imagens. Após o preenchimento, é possível salvar a inspeção.

Por fim, ao selecionar a opção Inspetores no menu lateral da tela 2, o administrador é redirecionado para a tela 3.1, que exibe uma lista de inspetores com barra de busca e informações básicas. Clicando em um dos inspetores, ele é levado à tela 3.1.2, onde são exibidos o nome, e-mail e as inspeções nas quais esse inspetor participou.

A seguir, apresenta-se o fluxo de telas idealizado para o perfil de inspetor, com base nas funcionalidades que compõem sua jornada dentro do sistema.

### Wireframe da tela na visão de inspetor:
<div align="center">
  <sub>FIGURA X - Wireframe de inspetor</sub><br>
  <img src= "../assets/wad/wireframeIsp.png" width="100%"
  alt="Wireframe login de inspetor"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

Novamente, para uma visualização mais detalhada, basta acessar o link: [Wireframes](https://www.figma.com/design/l96rnt77hzGwxLGk4mBpzO/Wireframes-Crtl-S?node-id=0-1&t=yvIYowEMqkph3kYD-1).

Considerando as [user stories](#23-user-stories-sprints-1-a-5) priorizadas nas sprints iniciais, especialmente aquelas atribuídas à persona [Laís Monteiro](#LaisMonteiro), o wireframe apresentado na figura acima representa a interface voltada à atuação da inspetora durante o processo de vistoria em campo. Este layout foi planejado para atender às necessidades de registros ágeis e confiáveis, respeitando o contexto de uso em locais com baixa conectividade e alto volume de informações técnicas.

O wireframe de baixa fidelidade prioriza funcionalidades essenciais para o momento da inspeção: o registro de imagens como evidência [(US02)](#US02), a categorização da patologia [(US01)](#US01), sua descrição textual e a indicação da localização. Todos os campos são acessíveis em uma única tela, reduzindo a necessidade de navegação e aumentando a fluidez do processo, aspecto fundamental para a jornada da persona identificada.

O sistema se inicia com a tela 1, onde o inspetor insere seu e-mail e senha para acessar a plataforma.

Após o login, o usuário é direcionado à tela 2, que contém as inspeções atribuídas a ele, apresentadas em formato de lista com filtro por status (a fazer, fazendo, feito) e campo de busca.

Ao selecionar uma inspeção, o inspetor acessa a tela 3 que possui as patologias daquela vistoria, onde ele pode consultar registros anteriores e/ou adicionar novos.

Clicando no botão “+”, o usuário é direcionado à tela 3.2 de registro da patologia, que permite anexar imagem, selecionar o tipo de patologia, descrever o problema e indicar a localização.

O campo de localização é interativo e direciona à tela 3.2.2, que exibe uma planta simplificada e permite preenchimento textual. Um botão ao lado permite salvar o local e retornar ao registro da patologia na tela 3.2.

Também é possível acessar uma tela de filtro de patologias, a tela 3.2.1, categorizadas por elementos construtivos (parede, teto, piso), para facilitar o agrupamento das anomalias encontradas.

Além disso, os botões de ação visíveis no rodapé na tela 3.2, um para adicionar novos registros e outro para confirmar a submissão, foram estrategicamente posicionados para permitir seu uso com apenas uma das mãos, levando em consideração a realidade operacional do inspetor em campo. A interface também inclui elementos visuais simples, evitando distrações que possam comprometer a eficiência da tarefa.

Por fim, há ainda a tela 3.1, que exibe um pré-relatório da inspeção. Essa tela só se torna acessível após todos os inspetores designados para aquela inspeção terem finalizado suas respectivas contribuições.

## 3.3. Guia de estilos 

Este guia padroniza os elementos visuais presentes no projeto, mantendo a paleta de cores, a tipografia e a iconografia de forma coerente com as exigências do IPT. A correta aplicação dessas diretrizes garante a uniformidade visual necessária para o projeto, fortalecendo sua identidade.

<div align="center">
  <sub>Guia de estilos</sub><br>
  <img src= "../assets/wad/guiaEstilos.png" width="100%" 
  alt="Guia de estilos"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

### 3.3.1 Cores

A paleta de cores da solução segue a identidade visual do IPT. As cores devem ser aplicadas conforme os padrões abaixo:

<div align="center">
  <sub>Paleta de cores IPT</sub><br>
  <img src= "../assets/wad/paletaDeCores.png" width="100%"
  alt="Paleta de cores"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>


**Preto Profundo**

RGB: R17 G20 B22

Hex: #111416

Função: Cor de fundo e elementos de alto contraste.

---
**Cinza Escuro**

RGB: R52 G58 B66

Hex: #343A42

Função: Fundo de seções, textos secundários e menus laterais.

---
**Cinza Médio 1**

RGB: R78 G81 B85

Hex: #4E5155

Função: Divisórias e áreas de apoio.

---
**Cinza Médio 2 (Pantone Cool Gray 9C)**

RGB: R117 G120 B123

Hex: #75787B

Função: Ícones, texto auxiliar, bordas.

---
**Cinza Claro**

RGB: R228 G228 B228

Hex: #E4E4E4

Função: Fundos de blocos de conteúdo.

---
**Branco**

RGB: R255 G255 B255

Hex: #FFFFFF

Função: Fundo principal e áreas de respiro.

---
**Azul Claro (Pantone 306C)**

RGB: R0 G181 B226

Hex: #00B5E2

Função: Destaques visuais e botões secundários.

---
**Azul Médio (Pantone 7462C)**

RGB: R0 G85 B140

Hex: #00558C

Função: Cabeçalhos e botões principais.

---

**Azul Escuro Profundo**

RGB: R0 G20 B70

Hex: #001446

Função: Destaques importantes e contraste elevado.

---
**Rosa Vibrante**

RGB: R233 G30 B99

Hex: #E91E63

Função: Chamadas para ação e alertas.

---

**Laranja Intenso**

RGB: R255 G129 B58

Hex: #FF813A

Função: Botões, ícones interativos e atenção.

---

**Amarelo Dourado**
RGB: R255 G177 B60

Hex: #FFB13C

Função: Indicadores positivos e destaques complementares.

---

### 3.3.2 Tipografia

A tipografia recomendada para garantir coerência com o guia de estilos do IPT inclui:

**1-Frutiger**

Função: Recomendada para para materiais que envolvam a marca do IPT.

**2-Myriad Pro**

Função: adequadas para peças de comunicação em geral.

**3-Calibri**

Função: pode ser utilizada em apresentações e documentos.


Portanto, nosso grupo optou por utilizar a tipografia Myriad Pro como principal fonte nas peças do projeto. Essa escolha foi baseada na sua versatilidade, legibilidade e na orientação do guia de estilos do IPT, que a recomenda para materiais de comunicação em geral. Além disso, a Myriad Pro transmite uma aparência moderna e acessível, o que contribui para uma comunicação mais clara e profissional com o público.

<div align="center">
  <sub>Tipografia Myriad Pro</sub><br>
  <img src= "../assets/wad/tipografia.png" width="100%" 
  alt="Tipografia"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

A imagem acima ilustra a aplicação da família tipográfica Myriad Pro, demonstrando sua variedade de pesos e estilos. Nela, é possível observar exemplos como:

- Myriad Pro Bold 32px usada em Títulos principais;

- Myriad Pro Bold Italic para ênfase em títulos;

- Myriad Pro Semi Bold e Semibold Italic, ideais para subtítulos e chamadas;

- Myriad Pro Regular e Light para legendas e textos corridos;

- Estilos Condensed (Regular, Bold, Italic) indicados para espaços reduzidos sem comprometer a leitura.

Essa diversidade de variações reforça a adaptabilidade da Myriad Pro para diferentes contextos do projeto, mantendo unidade visual e consistência com a identidade institucional do IPT.

### 3.3.3 Iconografia e imagens

O uso de ícones é fundamental para garantir uma comunicação visual eficiente e intuitiva dentro da interface. Ícones bem definidos mantêm a consistência visual com a identidade do sistema.

Em nosso projeto, selecionamos ícones baseando-nos em critérios como clareza semântica, simplicidade visual e coerência com a linguagem gráfica adotada (principalmente a tipografia Myriad Pro). Os ícones foram escolhidos com base em padrões de uso recorrentes em interfaces digitais modernas, garantindo familiaridade para o usuário final.

A imagem a seguir apresenta o conjunto de ícones definidos pelo grupo, com suas respectivas descrições e aplicações:

<div align="center">
  <sub>Ícones</sub><br>
  <img src= "../assets/wad/iconografia.png" width="100%" 
  alt="Ícones"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

## 3.4 Protótipo de alta fidelidade (sprint 3)

O protótipo de alta fidelidade é um mockup visual, sem implementação de código, que tem como objetivo proporcionar uma visão clara e realista de como será a aplicação web. A seguir, apresentamos as principais telas destinadas ao administrador:

Esta é a primeira interface acessada pelo administrador, projetada para visualização em desktop, onde ele realizará sua autenticação no sistema.

<div align="center">
  <sub>Tela de Login</sub><br>
  <img src= "../assets/wad/telaDeLogin.png" width="100%" 
  alt="Login"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

Após o login, o administrador é direcionado ao dashboard, que oferece uma visão geral dos principais elementos da aplicação, facilitando o gerenciamento e a navegação entre funcionalidades.

<div align="center">
  <sub>Tela de Dashboard</sub><br>
  <img src= "../assets/wad/telaDeDashboard.png" width="100%" 
  alt="Tela Dashboard"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

A partir do dashboard, o administrador pode acessar a tela de criação de uma nova inspeção, onde poderá preencher as informações necessárias para cadastrar uma nova atividade de vistoria.

<div align="center">
  <sub>Tela de Nova Inspeção</sub><br>
  <img src= "../assets/wad/telaDeNovaInspeção.png" width="100%" 
  alt="Nova Inspeção"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

Essas são as três telas principais voltadas à experiência do administrador. No entanto, o protótipo conta com outras interfaces complementares, que podem ser visualizadas integralmente por meio do link abaixo, com o intuito de manter a organização e facilitar a navegação entre as diferentes partes da aplicação.
link: https://www.figma.com/proto/RN7XO6itd0HtDVP0CuPh4F/Prototipo-crtl---s?node-id=6-169&t=GIW6CzFQjE7d1zIy-1

---

Dando continuidade à demonstração dos protótipos, também foi desenvolvido um protótipo específico para o perfil de Inspetor/Coordenador, com foco em usabilidade em dispositivos móveis. A seguir, é possível visualizar as principais telas destinadas a esse usuário.

<div align="center">
  <sub>Tela de Login Mobiler</sub><br>
  <img src= "../assets/wad/telaDeLoginMobile.png" width="40%" 
  alt="Login Inspetor/Coordenaodr"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

Logo após realizar o login, o Inspetor/Coordenador será direcionado para uma tela que exibe todas as inspeções atribuídas a ele:

<div align="center">
  <sub>Tela de todas as Inspeções Mobile</sub><br>
  <img src= "../assets/wad/telaDeTodasInspeçõesInspetor.png" width="40%" 
  alt="Login Inspetor/Coordenaodr"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

Ao selecionar uma das inspeções, o usuário será levado a uma tela com todos os detalhes e características referentes àquela inspeção específica:

<div align="center">
  <sub>Tela Característica Inspeções Mobile</sub><br>
  <img src= "../assets/wad/telaInspeção.png" width="40%" 
  alt="Inspeção Inspetor/Coordenaodr"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

Após selecionar uma de suas inspeções, o Inspetor/Coordenador pode adicionar uma nova patologia ao local correspondente ou cadastrar um novo local, caso necessário.

<div align="center">
  <sub>Tela de todas as Inspeções Mobile</sub><br>
  <img src= "../assets/wad/telaDeNovaPatologia.png" width="40%" 
  alt="Login Inspetor/Coordenador"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

Após cadastrar uma patologia, o Inspetor será redirecionado de volta à tela com as características da inspeção. Nessa tela, ele poderá finalizar a inspeção e visualizar um pré-relatório com um resumo de todas as informações coletadas durante o processo. A seguir, apresentamos um exemplo genérico desse pré-relatório:

<div align="center">
  <sub>Tela do Pré Relatório</sub><br>
  <img src= "../assets/wad/telaPréRelatório.png" width="40%" 
  alt="Pré Relatório"><br>
  <sup>Fonte: Material produzido pelos autores, 2025</sup>
</div>

Essas são as cinco telas principais voltadas à experiência do Inspetor/Coordenador. No entanto, o protótipo conta com outras interfaces complementares, que podem ser visualizadas integralmente por meio do link abaixo, com o intuito de manter a organização e facilitar a navegação entre as diferentes partes da aplicação.
link: https://www.figma.com/proto/RN7XO6itd0HtDVP0CuPh4F/Prototipo-crtl---s?node-id=1-1515&t=GIW6CzFQjE7d1zIy-0&scaling=min-zoom&content-scaling=fixed&page-id=0%3A1

## 3.5. Modelagem do banco de dados (sprints 2 e 4)

### 3.5.1. Modelo relacional (sprints 2 e 4)

O modelo relacional de banco de dados, originalmente proposto por Edgar Frank Codd na década de 1970, representa uma das formas mais eficientes e organizadas de estruturação de dados. Seu princípio fundamental é permitir que diferentes tabelas (ou entidades) possam ser relacionadas por meio de atributos em comum, sem a necessidade de reorganizar fisicamente os dados. Esse conceito proporciona flexibilidade, integridade e escalabilidade ao sistema, além de reduzir significativamente a redundância de informações (CODD, 1990)[⁷](#codd).

No contexto desse projeto, O modelo relacional foi idealizado com foco na organização, integridade e na rastreabilidade das informações relacionadas ao processo de inspeção predial. A modelagem foi realizada de maneira a representar com fidelidade os fluxos do sistema. Isso incluiu desde o cadastro de usuários e projetos até o registro detalhado de patologias observadas no campo.

A estrutura conta com elementos principais da operação refletidos nas seguintes entidades: usuários, permissões, projetos, edificações, inspeções, ambientes, sistemas construtivos, patologias, registros, status e anexos. Tal qual uma tabela, cada entidade foi definida, e seus atributos foram especificados com tipos adequados, assegurando performance e evitando redundância nas consultas.

Chaves estrangeiras foram utilizadas para estabelecer as relações entre as tabelas, permitindo vincular, por exemplo, um projeto ao edifício que está sendo inspecionado, assim como um registro à patologia observada. Além disso, foram adotadas tabelas associativas, como usuarios_projetos, para representar relações muitos-para-muitos (N:N) entre projetos e usuários, incluindo suas respectivas permissões.

As tabelas usam chaves primárias auto-incrementadas (id) para a identificação única e individual de todos os registros. Isso possibilita a integridade referencial. Atributos como usuario_projeto_id, sistema_patologia_id, ambiente_id e status_id atuam como ligações entre diferentes entidades do modelo, contribuindo para a coerência e consistência dos dados armazenados.

<div align="center">
<sub>Figura F - Modelo Relacional do Banco de Dados do Projeto IPT</sub>
<img src="../assets/wad/modeloLógico.png" width="100%" >
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

Para uma melhor experiência e compreensão do modelo relacional, disponibiliza-se o seguinte link, por meio do qual é possível acessar a diagramação completa e interativa da base de dados: [Diagrama do modelo relacional](https://dbdiagram.io/d/682782301227bdcb4eae757e). Essa visualização permite explorar, de forma mais precisa, os relacionamentos e os atributos de cada entidade presente na modelagem.

#### Entidades contidas no Banco de Dados

&nbsp;&nbsp;&nbsp;&nbsp;A entidade **usuarios**  representa os diferentes usuários com acesso à aplicação web, armazenando informações essenciais para sua autenticação e validação.

| Atributo | Descrição | Tipo |
|----------|-----------|------|
| id | Identificador único do usuário (chave primária) | INTEGER |
| nome | Nome completo do usuário | VARCHAR(100) |
| email | Endereço de e-mail do usuário (único) | VARCHAR(100) |
| senha | Senha criptografada do usuário | VARCHAR(100) |


&nbsp;&nbsp;&nbsp;&nbsp;A entidade **permissoes** define os diferentes níveis de acesso e função que um usuário pode exercer dentro de um projeto. Essa tabela é essencial para o controle de permissões dentro do sistema, diferenciando perfis como administrador, inspetor/coordenador ou inspetor. As permissões são atribuídas por meio da tabela associativa `usuarios_projetos`.

| Atributo | Descrição | Tipo |
|----------|-----------|------|
| id | Identificador único da permissão (chave primária) | INTEGER |
| nome | Nome do perfil de permissão (ex: administrador, inspetor) | VARCHAR(50) |


&nbsp;&nbsp;&nbsp;&nbsp;A entidade **edificios** representa as construções físicas que serão alvo das inspeções. Cada edifício possui informações como nome, endereço e datas da inspeção, além de estar associado a um status técnico definido pela entidade `status_edificios`. Essa tabela é essencial para registrar e organizar as obras inspecionadas no sistema.

| Atributo | Descrição | Tipo |
|----------|-----------|------|
| id | Identificador único do edifício (chave primária) | INTEGER |
| titulo_inspecao | Título de referência da inspeção no edifício | VARCHAR(100) |
| nome | Nome oficial do edifício | VARCHAR(100) |
| endereco | Endereço completo do edifício | VARCHAR(200) |
| data_inicio | Data de início da inspeção | DATE |
| data_fim | Data prevista ou real de conclusão da inspeção | DATE |
| tipo | Tipo de edificação (ex: residencial, comercial) | VARCHAR(100) |
| status_id | Identificador do status do edifício (chave estrangeira) | INTEGER |


&nbsp;&nbsp;&nbsp;&nbsp;A entidade **status_edificios** representa os diferentes estados possíveis de uma edificação no contexto de uma inspeção. Ela serve como base para indicar em que fase o edifício se encontra, como por exemplo: “em inspeção”, “concluído” ou “pendente”. Essa tabela é referenciada por `edificios` através do atributo `status_id`.

| Atributo | Descrição | Tipo |
|----------|-----------|------|
| id | Identificador único do status (chave primária) | INTEGER |
| descricao | Descrição textual do status da edificação | VARCHAR(50) |


&nbsp;&nbsp;&nbsp;&nbsp;A entidade **projetos** representa uma inspeção específica cadastrada para um determinado edifício. Cada projeto possui um cabeçalho descritivo, uma data de início e término, além da vinculação direta a um edifício já registrado no sistema. A partir de um projeto, é possível montar uma equipe de inspeção com diferentes usuários e permissões.

| Atributo | Descrição | Tipo |
|----------|-----------|------|
| id | Identificador único do projeto (chave primária) | INTEGER |
| cabecalho | Descrição resumida do projeto de inspeção | VARCHAR(100) |
| edificio_id | Identificador do edifício inspecionado (chave estrangeira) | INTEGER |
| data_inicio | Data de início prevista ou efetiva do projeto | DATE |
| data_fim | Data de encerramento prevista ou efetiva do projeto | DATE |


&nbsp;&nbsp;&nbsp;&nbsp;A entidade **usuarios_projetos** é uma tabela associativa responsável por relacionar usuários com projetos específicos. Ela também registra qual permissão o usuário exerce dentro daquele projeto. Com isso, permite-se que um mesmo usuário participe de múltiplos projetos com diferentes funções, como administrador, coordenador ou inspetor.

| Atributo | Descrição | Tipo |
|----------|-----------|------|
| id | Identificador único da associação (chave primária) | INTEGER |
| usuario_id | Identificador do usuário vinculado (chave estrangeira) | INTEGER |
| permissao_id | Identificador da permissão atribuída ao usuário (chave estrangeira) | INTEGER |
| projeto_id | Identificador do projeto vinculado (chave estrangeira) | INTEGER |


&nbsp;&nbsp;&nbsp;&nbsp;A entidade **inspecoes** representa uma inspeção realizada por um membro da equipe dentro de um projeto específico. Cada inspeção está vinculada a um usuário com função definida naquele projeto (via `usuarios_projetos`). Essa tabela é a base para os registros técnicos feitos durante a análise de um edifício.

| Atributo | Descrição | Tipo |
|----------|-----------|------|
| id | Identificador único da inspeção (chave primária) | INTEGER |
| usuario_projeto_id | Identificador do usuário no contexto do projeto (chave estrangeira) | INTEGER |


&nbsp;&nbsp;&nbsp;&nbsp;A entidade **status_registros** define os diferentes estados que um registro técnico pode assumir dentro do processo de inspeção. Essa classificação permite identificar a situação atual de uma ocorrência registrada, como por exemplo: pendente, em análise ou resolvido.

| Atributo | Descrição | Tipo |
|----------|-----------|------|
| id | Identificador único do status (chave primária) | INTEGER |
| descricao | Descrição do status atribuído ao registro | VARCHAR(50) |


&nbsp;&nbsp;&nbsp;&nbsp;A entidade **ambientes** lista os espaços internos ou externos de uma edificação que podem ser inspecionados. Cada ambiente pode ser utilizado para categorizar o local onde determinada patologia ou ocorrência foi registrada, como por exemplo: sala, corredor, área técnica.

| Atributo | Descrição | Tipo |
|----------|-----------|------|
| id | Identificador único do ambiente (chave primária) | INTEGER |
| nome | Nome do ambiente padronizado | VARCHAR(50) |


&nbsp;&nbsp;&nbsp;&nbsp;A entidade **sistemas** representa os diferentes sistemas construtivos presentes em uma edificação, como hidráulico, elétrico ou estrutural. Essa categorização técnica permite classificar onde ocorrem as patologias durante o processo de inspeção.

| Atributo | Descrição | Tipo |
|----------|-----------|------|
| id | Identificador único do sistema (chave primária) | INTEGER |
| nome | Nome do sistema construtivo | VARCHAR(50) |


&nbsp;&nbsp;&nbsp;&nbsp;A entidade **patologias** contém a lista de falhas, anomalias ou deteriorações que podem ser identificadas durante uma inspeção. Essas patologias estão ligadas aos sistemas e compõem a base do diagnóstico técnico da edificação.

| Atributo | Descrição | Tipo |
|----------|-----------|------|
| id | Identificador único da patologia (chave primária) | INTEGER |
| descricao | Descrição técnica da patologia observada | VARCHAR(100) |


&nbsp;&nbsp;&nbsp;&nbsp;A entidade **sistema_patologias** é uma tabela associativa que relaciona cada patologia com um sistema construtivo específico. Essa relação permite o reuso modular de patologias entre diferentes sistemas e fornece mais controle técnico sobre os tipos de falhas possíveis por categoria.

| Atributo | Descrição | Tipo |
|----------|-----------|------|
| id | Identificador único da associação (chave primária) | INTEGER |
| sistema_id | Identificador do sistema construtivo (chave estrangeira) | INTEGER |
| patologia_id | Identificador da patologia associada (chave estrangeira) | INTEGER |


&nbsp;&nbsp;&nbsp;&nbsp;A entidade **registros** representa as ocorrências documentadas durante uma inspeção, como o registro de uma patologia encontrada em um determinado ambiente. Cada registro está vinculado a uma inspeção, ambiente, sistema e patologia, além de conter status, data e observações detalhadas.

| Atributo | Descrição | Tipo |
|----------|-----------|------|
| id | Identificador único do registro (chave primária) | INTEGER |
| cabecalho | Cabeçalho ou título da ocorrência registrada | VARCHAR(100) |
| torre_bloco | Identificação da torre ou bloco do edifício | VARCHAR(50) |
| inspecao_id | Identificador da inspeção vinculada (chave estrangeira) | INTEGER |
| ambiente_id | Identificador do ambiente onde ocorreu o registro (chave estrangeira) | INTEGER |
| sistema_patologia_id | Identificador da associação entre sistema e patologia (chave estrangeira) | INTEGER |
| status_id | Identificador do status atual do registro (chave estrangeira) | INTEGER |
| data_registro | Data em que o registro foi efetuado | DATE |
| observacoes | Texto com informações adicionais ou comentários técnicos | TEXT |


&nbsp;&nbsp;&nbsp;&nbsp;A entidade **anexos** armazena os metadados de arquivos adicionados aos registros, como imagens, PDFs ou outros documentos técnicos. Esses arquivos são utilizados como evidência visual ou documental das patologias identificadas, enriquecendo os registros da inspeção.

| Atributo | Descrição | Tipo |
|----------|-----------|------|
| id | Identificador único do anexo (chave primária) | INTEGER |
| caminho_arquivo | Caminho ou URL do arquivo salvo no sistema | VARCHAR(255) |
| legenda | Texto explicativo ou título do anexo | VARCHAR(100) |


&nbsp;&nbsp;&nbsp;&nbsp;A entidade **patologia_anexos** é uma tabela associativa que vincula um anexo a uma patologia específica. Essa ligação permite relacionar evidências visuais diretamente às patologias registradas, mantendo uma estrutura organizada de documentação técnica.

| Atributo | Descrição | Tipo |
|----------|-----------|------|
| id | Identificador único da associação (chave primária) | INTEGER |
| patologia_id | Identificador da patologia relacionada (chave estrangeira) | INTEGER |
| anexo_id | Identificador do anexo relacionado (chave estrangeira) | INTEGER |


#### Relacionamentos entre Entidades

&nbsp;&nbsp;&nbsp;&nbsp;No contexto do banco de dados desenvolvido, os relacionamentos entre entidades refletem diretamente os fluxos funcionais da aplicação, como a montagem de equipes de inspeção, o vínculo entre registros e ambientes, e a associação de arquivos às patologias identificadas. As cardinalidades foram definidas com base nas regras de negócio levantadas nas User Stories e refletem associações do tipo um-para-um (1:1), um-para-muitos (1:N) e muitos-para-muitos (N:N).

&nbsp;&nbsp;&nbsp;&nbsp;Nos casos de relacionamentos muitos-para-muitos, foram utilizadas tabelas intermediárias, como `usuarios_projetos`, para representar corretamente a ligação entre duas entidades, mantendo a normalização e a integridade dos dados. A seguir, apresenta-se uma tabela que demonstra os relacionamentos existentes entre as entidades do sistema.

| Entidades relacionadas        | Descrição | Cardinalidade |
|------------------------------|-----------|---------------|
| `usuarios` e `usuarios_projetos` | Um usuário pode participar de vários projetos, com permissões distintas | (1,n) |
| `projetos` e `usuarios_projetos` | Um projeto pode ter múltiplos usuários associados | (1,n) |
| `usuarios_projetos` e `inspecoes` | Cada inspeção é feita por um usuário dentro de um projeto | (1,n) |
| `projetos` e `edificios`     | Um projeto está sempre vinculado a um único edifício | (1,1) |
| `inspecoes` e `registros`    | Cada inspeção pode gerar múltiplos registros | (1,n) |
| `registros` e `ambientes`    | Um registro é vinculado a um ambiente específico | (1,1) |
| `registros` e `sistema_patologias` | Cada registro refere-se a uma associação entre sistema e patologia | (1,1) |
| `sistemas` e `sistema_patologias` | Um sistema pode estar ligado a várias patologias | (1,n) |
| `patologias` e `sistema_patologias` | Uma patologia pode ocorrer em diferentes sistemas | (1,n) |
| `registros` e `status_registros` | Cada registro possui um status técnico | (1,1) |
| `edificios` e `status_edificios` | Cada edifício possui um status de inspeção associado | (1,1) |
| `patologias` e `patologia_anexos` | Uma patologia pode ter vários anexos vinculados | (1,n) |
| `anexos` e `patologia_anexos`     | Um anexo pode estar ligado a uma única patologia | (1,1) |


#### Modelagem Física do Banco de Dados

&nbsp;&nbsp;&nbsp;&nbsp;A modelagem física do banco de dados foi construída a partir do modelo relacional desenvolvido previamente, convertendo entidades e atributos conceituais em estruturas específicas para implementação no PostgreSQL. Nesta etapa, cada entidade se transforma em uma tabela, e os atributos passam a ser colunas com tipos de dados, restrições e chaves bem definidas.

&nbsp;&nbsp;&nbsp;&nbsp; Como visto previamente, na modelagem física, definimos para cada coluna: seu tipo de dado (como `VARCHAR`, `INTEGER`, `DATE`, `TEXT`), se é chave primária (`PRIMARY KEY`), chave estrangeira (`FOREIGN KEY`) ou possui alguma restrição adicional, como unicidade (`UNIQUE`). Além disso, o modelo respeita as ligações entre tabelas por meio das chaves estrangeiras, preservando a integridade referencial dos dados.

&nbsp;&nbsp;&nbsp;&nbsp;Essa estrutura permite que operações de inserção, consulta, atualização e exclusão (CRUD) sejam realizadas de forma otimizada, mesmo em cenários com grande volume de dados e complexidade nos relacionamentos. A arquitetura relacional adotada facilita a rastreabilidade e a categorização das inspeções, registros técnicos, usuários e patologias documentadas.

&nbsp;&nbsp;&nbsp;&nbsp;O código SQL que representa essa modelagem foi gerado a partir da ferramenta **DBDiagram.io**, utilizada durante o desenvolvimento da estrutura do banco. Abaixo segue um trecho do código `.sql` que representa a criação das tabelas com seus respectivos atributos e relacionamentos:

```sql
CREATE TABLE usuarios (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    senha VARCHAR(100) NOT NULL
);

CREATE TABLE status_edificios (
    id SERIAL PRIMARY KEY,
    descricao VARCHAR(50) NOT NULL
);

CREATE TABLE edificios (
    id SERIAL PRIMARY KEY,
    titulo_inspecao VARCHAR(100) NOT NULL,
    nome VARCHAR(100) NOT NULL,
    endereco VARCHAR(200) NOT NULL,
    data_inicio DATE NOT NULL,
    data_fim DATE NOT NULL,
    tipo VARCHAR(100) NOT NULL,
    status_id INTEGER NOT NULL,
    CONSTRAINT fk_status_edificio FOREIGN KEY (status_id) REFERENCES status_edificios(id)
);

CREATE TABLE permissoes (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(50) NOT NULL
);

CREATE TABLE projetos (
    id SERIAL PRIMARY KEY,
    cabecalho VARCHAR(100) NOT NULL,
    edificio_id INTEGER NOT NULL,
    data_inicio DATE NOT NULL,
    data_fim DATE NOT NULL,
    CONSTRAINT fk_edificio FOREIGN KEY (edificio_id) REFERENCES edificios(id)
);

CREATE TABLE usuarios_projetos (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL,
    permissao_id INTEGER NOT NULL,
    projeto_id INTEGER NOT NULL,
    CONSTRAINT fk_usuario FOREIGN KEY (usuario_id) REFERENCES usuarios(id),
    CONSTRAINT fk_permissao FOREIGN KEY (permissao_id) REFERENCES permissoes(id),
    CONSTRAINT fk_projeto FOREIGN KEY (projeto_id) REFERENCES projetos(id)
);

CREATE TABLE inspecoes (
    id SERIAL PRIMARY KEY,
    usuario_projeto_id INTEGER NOT NULL,
    CONSTRAINT fk_usuario_projeto FOREIGN KEY (usuario_projeto_id) REFERENCES usuarios_projetos(id)
);

CREATE TABLE status_registros (
    id SERIAL PRIMARY KEY,
    descricao VARCHAR(50) NOT NULL
);

CREATE TABLE ambientes (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(50) NOT NULL
);

CREATE TABLE sistemas (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(50) NOT NULL
);

CREATE TABLE patologias (
    id SERIAL PRIMARY KEY,
    descricao VARCHAR(100) NOT NULL
);

CREATE TABLE sistema_patologias (
    id SERIAL PRIMARY KEY,
    sistema_id INTEGER NOT NULL,
    patologia_id INTEGER NOT NULL,
    CONSTRAINT fk_sistema FOREIGN KEY (sistema_id) REFERENCES sistemas(id),
    CONSTRAINT fk_patologia FOREIGN KEY (patologia_id) REFERENCES patologias(id)
);

CREATE TABLE registros (
    id SERIAL PRIMARY KEY,
    cabecalho VARCHAR(100) NOT NULL,
    torre_bloco VARCHAR(50),
    inspecao_id INTEGER NOT NULL,
    ambiente_id INTEGER NOT NULL,
    sistema_patologia_id INTEGER NOT NULL,
    status_id INTEGER NOT NULL,
    data_registro DATE NOT NULL,
    observacoes TEXT,
    CONSTRAINT fk_inspecao FOREIGN KEY (inspecao_id) REFERENCES inspecoes(id),
    CONSTRAINT fk_ambiente FOREIGN KEY (ambiente_id) REFERENCES ambientes(id),
    CONSTRAINT fk_sistema_patologia FOREIGN KEY (sistema_patologia_id) REFERENCES sistema_patologias(id),
    CONSTRAINT fk_status_registro FOREIGN KEY (status_id) REFERENCES status_registros(id)
);

CREATE TABLE anexos (
    id SERIAL PRIMARY KEY,
    caminho_arquivo VARCHAR(255) NOT NULL,
    legenda VARCHAR(100)
);

CREATE TABLE patologia_anexos (
    id SERIAL PRIMARY KEY,
    patologia_id INTEGER NOT NULL,
    anexo_id INTEGER NOT NULL,
    CONSTRAINT fk_patologia FOREIGN KEY (patologia_id) REFERENCES patologias(id),
    CONSTRAINT fk_anexo FOREIGN KEY (anexo_id) REFERENCES anexos(id)
);
```

&nbsp;&nbsp;&nbsp;&nbsp;Após a execução do código, o diagrama físico, que contém informações sobre as tabelas, seus relacionamentos, colunas, tipos de dados e cardinalidade, é gerado no software PostgreSQL, conforme ilustrado na figura a seguir:

<div align="center">
<sub>Figura G - Banco de dados Físico</sub>
<img src="../assets/wad/supabase.png" width="100%" >
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

### 3.5.2. Consultas SQL e lógica proposicional 

### Consulta 1
Esta consulta tem como objetivo listar usuários com permissão de inspetor que estejam vinculados ao projeto de ID 2, ou então que tenham sido cadastrados recentemente no sistema (usuários com id > 100). Isso garante que tanto inspetores experientes alocados em projetos específicos quanto novos inspetores em processo de integração possam ser visualizados por um administrador ou coordenador.

**Expressão SQL (Inspetor):**
```sql
SELECT *
FROM usuarios
WHERE id
    IN (
        SELECT usuario_id
        FROM usuarios_projetos
        WHERE permissao_id = 3
            AND projeto_id = 2
        )
    OR id > 100;
```
#1 | ---
--- | ---
**Proposições lógicas** | $A$: O usuário tem permissão de inspetor (permissao_id = 3) <br> $B$: O usuário está no projeto 2 <br> $C$: O ID é maior que 100 (usuário cadastrado recentemente)
**Expressão lógica proposicional** | $(A \land B) \lor C$
**Tabela Verdade** | <table><thead><tr><th>$A$</th><th>$B$</th><th>$C$</th><th>$(A \land B)$</th><th>$(A \land B) \lor C$</th></tr></thead><tbody><tr><td>F</td><td>F</td><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>F</td><td>V</td><td>F</td><td>V</td></tr><tr><td>F</td><td>V</td><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>V</td><td>V</td><td>F</td><td>V</td></tr><tr><td>V</td><td>F</td><td>F</td><td>F</td><td>F</td></tr><tr><td>V</td><td>F</td><td>V</td><td>F</td><td>V</td></tr><tr><td>V</td><td>V</td><td>F</td><td>V</td><td>V</td></tr><tr><td>V</td><td>V</td><td>V</td><td>V</td><td>V</td></tr></tbody></table>

---
### Consulta 2
Esta consulta retorna a relação usuário-inspeção-permissão, expondo todos os usuários inclusos na inspeção analisada (exemplo: inspeção com id 1234) assim como suas respectivas permissões. Para evitar erros, também é analisado se o usuário existe e se este possui alguma permissão válida no projeto.

**Expressão SQL (Administrador):**
```sql
SELECT
    usu.id AS idUsuário,
    usu.nome,
    usu.email,
    perm.nome AS permissão,
    proj.id AS idProjeto,
    proj.cabecalho
FROM usuarios usu
JOIN usuarios_projetos usuProj
    ON usu.id = usuProj.usuario_id
JOIN permissoes perm
    ON perm.id = usuProj.permissao_id
JOIN projetos proj
    ON proj.id = usuProj.projeto_id
WHERE proj.id = 1234
    AND usu.id IS NOT NULL
    AND perm.id IS NOT NULL;
```
#2 | ---
--- | ---
**Proposições lógicas** | $A$: A inspeção tem id 1234 <br> $B$: O usuário correspondente tem id inválido (usuário não existe) <br> $C$: O usuário correspondente não possui permissão válida
**Expressão lógica proposicional** | $A \land \lnot B \land \lnot C$
**Tabela Verdade** | <table><thead><tr><th>$A$</th><th>$B$</th><th>$C$</th><th>$\lnot B$</th><th>$\lnot C$</th><th>$A \land \lnot B \land \lnot C$</th></tr></thead><tbody><tr><td>F</td><td>F</td><td>F</td><td>V</td><td>V</td><td>F</td></tr><tr><td>F</td><td>F</td><td>V</td><td>V</td><td>F</td><td>F</td></tr><tr><td>F</td><td>V</td><td>F</td><td>F</td><td>V</td><td>F</td></tr><tr><td>F</td><td>V</td><td>V</td><td>F</td><td>F</td><td>F</td></tr><tr><td>V</td><td>F</td><td>F</td><td>V</td><td>V</td><td>V</td></tr><tr><td>V</td><td>F</td><td>V</td><td>V</td><td>F</td><td>F</td></tr><tr><td>V</td><td>V</td><td>F</td><td>F</td><td>V</td><td>F</td></tr><tr><td>V</td><td>V</td><td>V</td><td>F</td><td>F</td><td>F</td></tr></tbody></table>

---

### Consulta 3
Esta consulta identifica quais são as informações que devem ser expostas com relação à inspeção analisada (exemplo: inspeção com id 1234) e todas aquelas que foram iniciadas a menos de 1 ano atrás, incluindo uma lista de todos os coordenadores, uma lista de todos os inspetores, id do projeto, cabecalho, datas de inicio e fim, id do edificio, endereço, tipo do edificio (residencial, industrial) e status. Para evitar erros, também é analisado se as inspeções estão conectadas a um edificio existente.

**Expressão SQL:**
```sql
SELECT
    proj.id AS idProjeto,
    (SELECT STRING_AGG(usu.nome, ', ') -- Cria uma lista com todos os nomes
    FROM usuarios usu
    JOIN usuarios_projetos usuProj
        ON usu.id = usuProj.usuario_id
    WHERE usuProj.projeto_id = proj.id
        AND usuProj.permissao_id = 2
    )
    AS nomesCoordenadores,
    (SELECT STRING_AGG(usu.nome, ', ') -- Cria uma lista com todos os nomes
    FROM usuarios usu
    JOIN usuarios_projetos usuProj
        ON usu.id = usuProj.usuario_id
    WHERE usuProj.projeto_id = proj.id
        AND usuProj.permissao_id = 3
    )
    AS nomesInspetores,
    proj.cabecalho,
    proj.data_inicio,
    proj.data_fim,
    edif.id AS idEdificio,
    edif.nome AS nomeEdificio,
    edif.endereco,
    edif.tipo AS tipoEdificio,
    statEdif.descricao AS status
FROM projetos proj
JOIN edificios edif
    ON edif.id = proj.edificio_id
JOIN status_edificios statEdif
    ON statEdif.id = edif.status_id
WHERE proj.id = 1234
    OR (
    proj.data_inicio > NOW() - INTERVAL '1 year'
    AND proj.data_inicio <= NOW()
    )
    AND edif.id IS NOT NULL;
```
#3 | ---
--- | ---
**Proposições lógicas** | $A$: O projeto tem o id 1234 <br> $B$: O projeto ocorreu a menos de um ano atrás <br> $C$: O projeto foi iniciado no passado <br> $D$: O projeto não está vinculado a um edificio válido
**Expressão lógica proposicional** | $(A \lor (B \land C)) \land \lnot D$
**Tabela Verdade** | <table><thead><tr><th>$A$</th><th>$B$</th><th>$C$</th><th>$D$</th><th>$B \land C$</th><th>$A \lor (B \land C)$</th><th>$\lnot D$</th><th>$(A \lor (B \land C)) \land \lnot D$</th></tr></thead><tbody><tr><td>F</td><td>F</td><td>F</td><td>F</td><td>F</td><td>F</td><td>V</td><td>F</td></tr><tr><td>F</td><td>F</td><td>F</td><td>V</td><td>F</td><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>F</td><td>V</td><td>F</td><td>F</td><td>F</td><td>V</td><td>F</td></tr><tr><td>F</td><td>F</td><td>V</td><td>V</td><td>F</td><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>V</td><td>F</td><td>F</td><td>F</td><td>F</td><td>V</td><td>F</td></tr><tr><td>F</td><td>V</td><td>F</td><td>V</td><td>F</td><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>V</td><td>V</td><td>F</td><td>V</td><td>V</td><td>V</td><td>V</td></tr><tr><td>F</td><td>V</td><td>V</td><td>V</td><td>V</td><td>V</td><td>F</td><td>F</td></tr><tr><td>V</td><td>F</td><td>F</td><td>F</td><td>F</td><td>V</td><td>V</td><td>V</td></tr><tr><td>V</td><td>F</td><td>F</td><td>V</td><td>F</td><td>V</td><td>F</td><td>F</td></tr><tr><td>V</td><td>F</td><td>V</td><td>F</td><td>F</td><td>V</td><td>V</td><td>V</td></tr><tr><td>V</td><td>F</td><td>V</td><td>V</td><td>F</td><td>V</td><td>F</td><td>F</td></tr><tr><td>V</td><td>V</td><td>F</td><td>F</td><td>F</td><td>V</td><td>V</td><td>V</td></tr><tr><td>V</td><td>V</td><td>F</td><td>V</td><td>F</td><td>V</td><td>F</td><td>F</td></tr><tr><td>V</td><td>V</td><td>V</td><td>F</td><td>V</td><td>V</td><td>V</td><td>V</td></tr><tr><td>V</td><td>V</td><td>V</td><td>V</td><td>V</td><td>V</td><td>F</td><td>F</td></tr></tbody></table>

---
### Consulta 4
Esta consulta insere uma nova instância de usuário no banco de dados com os valores inseridos pelo próprio usuário, a não ser seu id, que é escolhido automaticamente. Também, é analisado se o email inserido ja está no banco de dados, evitando contas com emails idênticos, ajudando na recuperação de conta. Por segurança, a senha será modificada no banco de dados para ser hash com o objetivo de evitar comprometimentos.

**Expressão SQL:**
```sql
INSERT INTO usuarios (nome, email, senha)
SELECT
    'Exemplo Nome',
    '<EMAIL>',
    '$2b$10$K8X...senhaComHash'
WHERE NOT EXISTS (
    SELECT 1
    FROM usuarios
    WHERE email = '<EMAIL>'
);
```
#4 | ---
--- | ---
**Proposições lógicas** | $A$: O usuário inseriu os dados necessários <br> $B$: O usuário inseriu um email que já existe em outra conta
**Expressão lógica proposicional** | $A \land \lnot B$
**Tabela Verdade** | <table><thead><tr><th>$A$</th><th>$B$</th><th>$\lnot B$</th><th>$A \land \lnot B$</th></tr></thead><tbody><tr><td>F</td><td>F</td><td>V</td><td>F</td></tr><tr><td>F</td><td>V</td><td>F</td><td>F</td></tr><tr><td>V</td><td>F</td><td>V</td><td>V</td></tr><tr><td>V</td><td>V</td><td>F</td><td>F</td></tr></tbody></table>

---
### Consulta 5
Esta consulta tem como objetivo atualizar a coluna senha da tabela usuarios para o valor '321senha' apenas para o usuário cujo email seja exatamente '<EMAIL>' e, além disso, termine com '.com'. Essa verificação extra com "LIKE '%.com'" garante que a alteração só ocorrerá se o email realmente terminar com '.com', funcionando como uma camada a mais de validação.

**Expressão SQL**
```sql
UPDATE usuarios
SET senha = '321senha'
WHERE email = '<EMAIL>'
    AND email LIKE '%.com';
```
#5 | ---
--- | ---
**Proposições lógicas** | $A$: O email do usuário é exatamente '<EMAIL>' (email = '<EMAIL>') <br> $B$: O email do usuário termina com .com (email LIKE '%.com')
**Expressão lógica proposicional** | $A \land B$
**Tabela Verdade** | <table><thead><tr><th>$A$</th><th>$B$</th><th>$(A \land B)$</th></tr></thead><tbody><tr><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>V</td><td>F</td></tr><tr><td>V</td><td>F</td><td>F</td></tr><tr><td>V</td><td>V</td><td>V</td></tr></tbody></table>

---
### Consulta 6
A consulta SQL a seguir exclui da tabela usuarios todos os usuários que atendam a pelo menos uma das seguintes condições: possuem o id igual a 123 ou possuem o email igual a '<EMAIL>'. Isso significa que qualquer usuário com esse ID ou com esse email será deletado da base, mas, preferivelmente, será deletado apenas um usuário, já que ambos os atributos id e email são únicos a cada usuário.

**Expressão SQL**
```sql
DELETE FROM usuarios
WHERE id = 123
    OR email = '<EMAIL>';
```
#6 | ---
--- | ---
**Proposições lógicas** | $A$: O usuário possui o ID igual a 123 (id = 123) <br> $B$: O usuário possui o email igual a '<EMAIL>' (email = '<EMAIL>')
**Expressão lógica proposicional** | $A \lor B$
**Tabela Verdade** | <table><thead><tr><th>$A$</th><th>$B$</th><th>$(A \lor B)$</th></tr></thead><tbody><tr><td>F</td><td>F</td><td>F</td></tr><tr><td>F</td><td>V</td><td>V</td></tr><tr><td>V</td><td>F</td><td>V</td></tr><tr><td>V</td><td>V</td><td>V</td></tr></tbody></table>

---
### Consulta 7
A consulta SQL a seguir insere um novo registro na tabela registros com as informações fornecidas (como cabeçalho, torre, IDs, data e observação). No entanto, essa inserção só ocorrerá caso ainda não exista um registro com a mesma combinação de inspecao_id e ambiente_id. Isso garante que registros duplicados não sejam criados para a mesma inspeção e ambiente.

**Expressão SQL**
```sql
INSERT INTO registros (
    cabecalho,
    torre_bloco,
    inspecao_id,
    ambiente_id,
    sistema_patologia_id,
    status_id,
    data_registro,
    observacoes
    )
    SELECT
        'exemplo cabeçalho',
        'Torre B',
        1234,
        5678,
        91011,
        1,
        '2003-02-01',
        'Observação exemplo'
WHERE NOT EXISTS (
    SELECT 1
    FROM registros
    WHERE inspecao_id = 1234
        AND ambiente_id = 5678
    );
```
#7 | ---
--- | ---
**Proposições lógicas** |$A$:	Existe um registro com o mesmo inspecao_id = 1234.<br> $B$:	Existe um registro com o mesmo ambiente_id = 5678.
**Expressão lógica proposicional** | $\neg (A \land B)$
**Tabela Verdade** | <table><thead><tr><th>$A$</th><th>$B$</th><th>$(A \land B)$</th><th>$\neg (A \land B)$</th></tr></thead><tbody><tr><td>F</td><td>F</td><td>F</td><td>V</td></tr><tr><td>F</td><td>V</td><td>F</td><td>V</td></tr><tr><td>V</td><td>F</td><td>F</td><td>V</td></tr><tr><td>V</td><td>V</td><td>V</td><td>F</td></tr></tbody></table>

## 3.6. WebAPI e endpoints (sprints 3 e 4)

A documentação completa dos endpoints da API REST do sistema CTRL + S está disponível no arquivo dedicado:

**📄 [Documentação Completa dos Endpoints](./endpoints.md)**

### Resumo da API

A API REST do sistema CTRL + S foi desenvolvida seguindo os padrões RESTful, oferecendo endpoints para gerenciamento completo de inspeções prediais. A API utiliza JSON como formato de dados e implementa códigos de status HTTP apropriados.

**Base URL:** `http://localhost:3000`

**Principais recursos disponíveis:**
- **Usuários** - Gerenciamento de usuários e autenticação
- **Projetos** - Criação e gestão de projetos de inspeção
- **Inspeções** - Controle do processo de inspeção
- **Patologias** - Cadastro e classificação de patologias
- **Edifícios** - Informações dos edifícios inspecionados
- **Ambientes** - Divisões e ambientes dos edifícios
- **Sistemas** - Sistemas prediais (elétrico, hidráulico, etc.)
- **Anexos** - Gerenciamento de imagens e documentos

**Métodos HTTP utilizados:**
- `GET` - Consulta de dados
- `POST` - Criação de novos recursos
- `PUT` - Atualização completa de recursos
- `DELETE` - Remoção de recursos

Cada endpoint implementa validação de dados, tratamento de erros apropriado e retorna códigos de status HTTP padronizados (200, 201, 400, 404, 500).

# <a name="c4"></a>4. Desenvolvimento da Aplicação Web

## 4.1. Primeira versão da aplicação web (sprint 3)

Nesta etapa do projeto, finalizamos a primeira versão da aplicação web desenvolvida para otimizar o trabalho de inspeções prediais no IPT. O processo envolveu a elaboração de um protótipo de alta fidelidade, que representa com precisão o layout e a interação da interface, e a criação de um guia de estilos, responsável por padronizar elementos visuais como cores, tipografia e espaçamentos, garantindo coesão e qualidade estética ao sistema.

Além disso, definimos a arquitetura da solução, estabelecendo a estrutura técnica que orienta o desenvolvimento e assegura a organização do código. Por fim, realizamos a implementação inicial do sistema, com as principais telas e funcionalidades, utilizando tecnologias essenciais para o desenvolvimento web: HTML e CSS.

HTML (HyperText Markup Language) é a linguagem de marcação que estrutura e organiza os elementos da interface, como textos, botões, formulários e imagens. Já o CSS (Cascading Style Sheets) é a linguagem responsável pela estilização desses elementos, definindo aspectos visuais como cores, fontes, tamanhos e espaçamentos.

A combinação dessas duas tecnologias permitiu transformar as ideias e protótipos em uma aplicação funcional, intuitiva e visualmente consistente, alinhada às necessidades do IPT e aos objetivos do projeto.

### Layout

O layout da aplicação foi concebido para oferecer uma navegação simples, intuitiva e adaptada aos diferentes perfis de usuário e dispositivos. Na versão para web desktop, destinada ao administrador, a interface conta com uma barra lateral fixa que acompanha o usuário em todas as principais telas do sistema, incluindo as seções de início, inspeções e usuários. Essa barra lateral concentra os principais pontos de acesso, facilitando a navegação e a execução das tarefas administrativas.

<div align="center">
<sub>Figura G - Barra Lateral para Desktop </sub><br>
<img src="../assets/wad/barraLateral.png" width="30%" align="center"><br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

Por outro lado, na versão mobile, voltada para o inspetor, a navegação é realizada por meio de uma barra inferior, que permanece visível em todas as telas principais da aplicação, como as seções de inspeções e perfil. Essa adaptação visa otimizar a usabilidade em dispositivos móveis, garantindo que as funcionalidades mais importantes estejam sempre acessíveis, sem comprometer a simplicidade e a clareza da interface.

<div align="center">
<sub>Figura G - Footer para mobile</sub><br>
<img src="../assets/wad/footer.png" width="70%" align="center"><br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div><br>

 Para a estruturação da Barra Lateral e Footer utilizamos o HTML e JS, para a estilização dos elementos foi utilizado o CSS.

 ### Body Desktop

O body da aplicação, nesta primeira versão, foi estruturado com HTML básico, priorizando a simplicidade e a organização dos elementos na tela. A estrutura contém as seções principais necessárias para a navegação e exibição de informações, seguindo as boas práticas de marcação sem ainda incorporar estilos ou comportamentos avançados.

#### Dashboard (Administrador - Desktop)
Na página de Dashboard, o administrador visualiza um panorama geral das operações, com informações organizadas em blocos: inspeções recentes, estatísticas de projetos, relatórios e alertas, além da atividade recente da equipe. A disposição busca facilitar o acompanhamento e a tomada de decisão.

<div align="center">
<sub>Figura G - Página Dashboard</sub><br>
<img src="../assets/wad/dashboardDesktop.png" width="50%" align="center"><br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

<div align="center">
<sub>Figura G - Página Dashboard (continuação)</sub><br>
<img src="../assets/wad/dashboard2Desktop.png" width="70%" align="center"><br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

#### Inspeções (Administrador - Desktop)
A página de Inspeções oferece ao administrador a possibilidade de visualizar todas as inspeções, classificadas conforme o status: Não iniciado, Em andamento e Concluído. Também foram inseridos filtros, uma barra de pesquisa e um botão para criar novas inspeções, compondo a base funcional da gestão das atividades.

<div align="center">
<sub>Figura G - Página inspeções</sub><br>
<img src="../assets/wad/ispecoesDesktop.png" width="50%" align="center"><br>
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

Toda essa estrutura foi implementada em HTML básico, servindo como esqueleto da aplicação. Em versões futuras, vamos evoluir a interface, incorporando estilos, elementos interativos e ajustes visuais para torná-la mais fiel ao protótipo de alta fidelidade desenvolvido pela equipe.

 ### Body Mobile

Nesta versão inicial do sistema, desenvolvemos uma interface mobile em HTML e CSS básicos, com foco em estabelecer a estrutura principal e testar os fluxos de navegação.

Tela de Inspeções: permite que o inspetor visualize as inspeções que ele está alocado.

 <div align="center">
<sub>Figura G - Página inspeções</sub>
<img src="../assets/wad/inspecoesMobile.png" width="100%" >
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

Tela de Adicionar Registro: facilita o registro de dados coletados em campo, com foco na agilidade e clareza.

 <div align="center">
<sub>Figura G - Página Adicionar Registro</sub>
<img src="../assets/wad/registroMobile.png" width="100%" >
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

Tela de Relatório: possibilita a consulta rápida ao relatório das inspeções realizadas.

 <div align="center">
<sub>Figura G - Página Relatório</sub>
<img src="../assets/wad/relatorioMobile.png" width="100%" >
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

Tela de Perfil: centraliza as informações do usuário, permitindo ajustes e visualização de dados pessoais, além das inspeções que o usuário está participando.

 <div align="center">
<sub>Figura G - Página Perfil</sub>
<img src="../assets/wad/perfilMobile.png" width="100%" >
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

Tela de Listagem de Patologias: exibe de forma organizada todas as patologias encontradas e relatadas em determinada inspeção.

 <div align="center">
<sub>Figura G - Página Patologias</sub>
<img src="../assets/wad/patologiasMobile.png" width="100%" >
<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

Para as próximas versões, está previsto o aprimoramento da interface, com a aplicação de estilos mais sofisticados, responsividade avançada e melhor experiência de usuário (UX).

### Integração com Banco de Dados Relacional

Nesta primeira versão do sistema, seguimos a arquitetura MVC (Model-View-Controller), um padrão amplamente utilizado no desenvolvimento de software para promover a separação de responsabilidades.

- Model: representa os dados e as regras de negócio.

- View: cuida da apresentação das informações para o usuário.

- Controller: faz a mediação entre Model e View, processando requisições, manipulando dados e definindo as respostas.

Essa estrutura facilita a organização, manutenção e escalabilidade do sistema, garantindo que cada componente tenha uma função bem definida.

#### Models

A seguir, apresentamos o código da model Ambiente, que encapsula toda a lógica de acesso e manipulação dos dados relacionados à tabela ambientes no banco de dados relacional.

```js
const db = require('../config/db');

class Ambiente {
  // Lista todos os ambientes
  static async todos() {
    const result = await db.query('SELECT * FROM ambientes');
    return result.rows;
  }

  // Busca um ambiente pelo ID
  static async buscarPorId(id) {
    const result = await db.query('SELECT * FROM ambientes WHERE id = $1', [id]);
    return result.rows[0];
  }

  // Insere um novo ambiente
  static async criar(ambiente) {
    const result = await db.query(
      'INSERT INTO ambientes (projeto_id, nome, possui_planta) VALUES ($1, $2, $3) RETURNING *',
      [ambiente.projeto_id, ambiente.nome, ambiente.possui_planta]
    );
    return result.rows[0];
  }

  // Atualiza um ambiente existente
  static async atualizar(id, dados) {
    const result = await db.query(
      'UPDATE ambientes SET projeto_id = $1, nome = $2, possui_planta = $3 WHERE id = $4 RETURNING *',
      [dados.projeto_id, dados.nome, dados.possui_planta, id]
    );
    return result.rows[0];
  }

  // Deleta um ambiente pelo ID
  static async deletar(id) {
    const result = await db.query('DELETE FROM ambientes WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = Ambiente;
```

Este model é responsável por implementar todas as operações CRUD (Create, Read, Update, Delete), garantindo uma interface padronizada e segura para que outras partes do sistema interajam com os dados.

A conexão com o banco é realizada através do módulo db, previamente configurado, que permite a execução de comandos SQL utilizando parâmetros dinamicamente, o que reforça a segurança contra injeção de SQL.

O código define os seguintes métodos:

- todos(): recupera todos os registros da tabela ambientes.

- buscarPorId(id): busca um ambiente específico, a partir do seu identificador único.

- criar(ambiente): insere um novo ambiente no banco, retornando o registro recém-criado.

- atualizar(id, dados): atualiza os campos de um ambiente existente, identificado por id.

- deletar(id): remove definitivamente um ambiente da base de dados.

Cada método faz uso de queries parametrizadas e da sintaxe assíncrona (async/await), garantindo operações não bloqueantes e seguras.

Com esse model, estabelecemos uma camada consistente de abstração para a manipulação de dados, centralizando as regras de acesso e permitindo que o restante do sistema trabalhe com os ambientes de forma mais simples.

#### Views

A seguir, apresentamos o código da view da funcionalidade "Ambiente", responsável por exibir e permitir a interação do usuário com a planta baixa de um ambiente, além de registrar informações relacionadas à localização de patologias identificadas.

```html
<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>Ambiente</title>
  <style>
    * { box-sizing: border-box; }
    body {
      font-family: sans-serif;
      margin: 0;
      background: #fff;
      padding-bottom: 120px;
    }

    header {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px;
      font-weight: bold;
      font-size: 1.3rem;
      border-bottom: 1px solid #ddd;
      position: relative;
    }

    .back-btn {
      position: absolute;
      left: 16px;
      font-size: 24px;
      cursor: pointer;
    }

    main {
      max-width: 480px;
      margin: 0 auto;
      padding: 1rem;
    }

    .planta-container img {
      width: 100%;
      border-radius: 12px;
    }

    .planta-container p {
      text-align: center;
      font-size: 0.9rem;
      color: #777;
      margin-top: 6px;
      font-style: italic;
    }

    textarea {
      width: 100%;
      padding: 12px;
      border-radius: 10px;
      border: 1px solid #ccc;
      margin-top: 16px;
      font-size: 0.95rem;
    }

    h3 {
      margin-top: 32px;
      font-size: 1rem;
    }

    .btn {
      margin-top: 24px;
      width: 100%;
      background-color: #f72585;
      color: white;
      font-size: 1rem;
      font-weight: bold;
      padding: 14px 0;
      border: none;
      border-radius: 16px;
      cursor: pointer;
    }

    .bottom-nav {
      position: fixed;
      bottom: 0;
      width: 100%;
      background-color: #f5f5f5;
      display: flex;
      justify-content: space-around;
      padding: 12px 0;
      border-top: 1px solid #ddd;
    }

    .bottom-nav div {
      text-align: center;
      font-size: 14px;
      cursor: pointer;
    }

    .bottom-nav img {
      width: 24px;
      height: auto;
      margin: 0 auto 4px;
      display: block;
    }
  </style>
</head>
<body>

  <header>
    <span class="back-btn" onclick="history.back()">×</span>
    Ambiente
  </header>

  <main>
    <div class="planta-container">
      <img id="planta-img" src="../assets/planta.png" alt="Planta baixa">
      <p>Desenhe na imagem acima o local onde a patologia foi registrada</p>
    </div>

    <h3>Caso não haja planta, descreva aqui sua posição</h3>
    <textarea id="descricao-local" placeholder="Adicione uma descrição para a sua localização"></textarea>

    <button class="btn" onclick="window.location.href='registro.html'">ADICIONAR LOCALIZAÇÃO</button>
  </main>

  <div class="bottom-nav">
    <div onclick="location.href='inspecoes.html'">
      <img src="../assets/Icons/paste.png" alt="Inspeções">
      <div>Inspeções</div>
    </div>
    <div onclick="location.href='../views/perfil.html'">
      <img src="../assets/Icons/profile.png" alt="Perfil">
      <div>Perfil</div>
    </div>
  </div>

  <script>
    function adicionarLocal() {
      const texto = document.getElementById('descricao-local').value.trim();
      const localSalvo = texto !== "" ? texto : "Selecionado na planta";
      localStorage.setItem('localPatologia', localSalvo);
      window.location.href = 'registro.html';
    }
  </script>

</body>
</html>
```

Essa view foi implementada utilizando HTML e CSS puros, priorizando a simplicidade e a responsividade da interface, adaptando-se a dispositivos móveis e desktops. Além disso, conta com um pequeno script em JavaScript para interação básica com o usuário.

O layout e a estrutura da view incluem os seguintes elementos principais:

- Cabeçalho (header): exibe o título "Ambiente" e um botão de voltar (back-btn), que utiliza history.back() para retornar à página anterior.

- Área de visualização (.planta-container): apresenta uma imagem representando a planta baixa do ambiente, onde o usuário pode visualizar ou imaginar o local da patologia.

- Campo de descrição (textarea): permite que o usuário descreva manualmente a localização do problema, caso não consiga ou não queira indicar diretamente na planta.

- Botão de ação: leva o usuário para a próxima etapa ("registro.html"), onde a localização é consolidada.

- Navegação inferior (.bottom-nav): oferece atalhos para as principais seções da aplicação, como "Inspeções" e "Perfil", promovendo uma navegação intuitiva.

- Script adicionarLocal(): função JavaScript responsável por capturar a descrição inserida pelo usuário ou, caso ausente, definir automaticamente que a localização foi indicada na planta. Essa informação é temporariamente armazenada no localStorage para ser utilizada na próxima etapa do fluxo.

A view, portanto, cumpre a função essencial de apresentar dados e capturar interações do usuário, servindo como uma ponte visual e funcional entre o usuário e a lógica implementada nos controllers e models.

Com essa implementação, garantimos uma experiência de uso direta, fluida e que prepara terreno para futuras melhorias, como a inclusão de funcionalidades mais avançadas de desenho ou marcação diretamente na planta.

#### Controllers

O Controller é a camada responsável por intermediar as interações do usuário com o sistema, processando as requisições HTTP recebidas, delegando as operações de manipulação de dados para os Models e enviando as respostas adequadas.

```js
const Ambiente = require('../models/ambientesModels');

// Lista todos os ambientes
const listarAmbientes = async (req, res) => {
  try {
    const ambientes = await Ambiente.todos();
    res.status(200).json(ambientes);
  } catch (error) {
    console.error('Erro ao listar ambientes:', error);
    res.status(500).json({ erro: 'Erro ao listar ambientes.' });
  }
};

// Busca um ambiente pelo ID
const buscarAmbientePorId = async (req, res) => {
  try {
    const ambiente = await Ambiente.buscarPorId(req.params.id);
    if (ambiente) {
      res.status(200).json(ambiente);
    } else {
      res.status(404).json({ erro: 'Ambiente não encontrado.' });
    }
  } catch (error) {
    console.error('Erro ao buscar ambiente:', error);
    res.status(500).json({ erro: 'Erro ao buscar ambiente.' });
  }
};

// Cria um novo ambiente
const criarAmbiente = async (req, res) => {
  try {
    const novoAmbiente = await Ambiente.criar(req.body);
    res.status(201).json(novoAmbiente);
  } catch (error) {
    console.error('Erro ao criar ambiente:', error);
    res.status(500).json({ erro: 'Erro ao criar ambiente.' });
  }
};

// Atualiza um ambiente existente
const atualizarAmbiente = async (req, res) => {
  try {
    const ambienteAtualizado = await Ambiente.atualizar(req.params.id, req.body);
    if (ambienteAtualizado) {
      res.status(200).json(ambienteAtualizado);
    } else {
      res.status(404).json({ erro: 'Ambiente não encontrado para atualização.' });
    }
  } catch (error) {
    console.error('Erro ao atualizar ambiente:', error);
    res.status(500).json({ erro: 'Erro ao atualizar ambiente.' });
  }
};

// Deleta um ambiente pelo ID
const deletarAmbiente = async (req, res) => {
  try {
    const sucesso = await Ambiente.deletar(req.params.id);
    if (sucesso) {
      res.status(200).json({ mensagem: 'Ambiente deletado com sucesso.' });
    } else {
      res.status(404).json({ erro: 'Ambiente não encontrado para exclusão.' });
    }
  } catch (error) {
    console.error('Erro ao deletar ambiente:', error);
    res.status(500).json({ erro: 'Erro ao deletar ambiente.' });
  }
};

module.exports = {
  listarAmbientes,
  buscarAmbientePorId,
  criarAmbiente,
  atualizarAmbiente,
  deletarAmbiente,
};
```

No caso deste sistema, o ambientesController implementa todas as operações básicas do CRUD (Create, Read, Update, Delete) para a entidade "Ambiente". Ele atua de forma assíncrona para garantir que a comunicação com o banco de dados seja realizada sem bloquear o fluxo de execução da aplicação.

Cada função do controller segue a estrutura de tratamento de exceções, utilizando try...catch para capturar e lidar com erros que possam ocorrer durante as operações de banco de dados.

A seguir, detalhamos cada função:

#### listarAmbientes
- Recebe uma requisição para listar todos os ambientes cadastrados no banco.
- Chama o método Ambiente.todos() (do Model), que executa a consulta SQL correspondente.
- Retorna um JSON com os ambientes encontrados ou, em caso de falha, uma mensagem de erro com status HTTP 500.

#### buscarAmbientePorId
- Recebe um id como parâmetro da requisição (req.params.id).
- Utiliza o método Ambiente.buscarPorId(id) para buscar no banco o ambiente correspondente.
- Caso encontrado, responde com os dados e status 200; se não, retorna erro 404.
- Eventuais erros de execução geram resposta com status 500.

#### criarAmbiente
- Recebe os dados do novo ambiente via req.body.
- Chama Ambiente.criar(req.body) para inserir o registro no banco.
- Retorna os dados do ambiente recém-criado com status HTTP 201 (Created).
- Falhas na inserção resultam em erro 500.

#### atualizarAmbiente
- Recebe o id do ambiente a ser atualizado e os novos dados via req.body.
- Chama Ambiente.atualizar(id, req.body) para modificar o registro no banco.
- Se a atualização for bem-sucedida, responde com os dados atualizados e status 200; caso o ambiente não exista, responde com erro 404.
- Qualquer erro de processamento gera status 500.

#### deletarAmbiente
- Recebe o id do ambiente a ser excluído.
- Executa Ambiente.deletar(id) para remover o registro correspondente.
- Caso a exclusão seja confirmada, responde com status 200 e uma mensagem de sucesso; caso contrário, retorna erro 404.
- Problemas de execução resultam em resposta com status 500.

Este controller implementa de forma clara e organizada os fluxos de interação com o recurso "Ambiente", garantindo que todas as operações CRUD sejam acessíveis através de requisições HTTP apropriadas, com tratamento consistente de erros e respostas. Ele é o elo fundamental entre as regras de negócio implementadas no Model e a apresentação e navegação realizadas pela View.

### Dificuldades enfrentadas no desenvolvimento

A integração inicial com o banco de dados apresentou alguns desafios, especialmente no mapeamento das relações entre tabelas e na otimização das consultas para garantir um desempenho adequado. Durante esse processo, foi necessário ajustar a estrutura do banco e realizar alterações frequentes para assegurar a integridade e eficiência do sistema.

Além disso, tivemos que adaptar as rotas e endpoints da aplicação para acompanhar as mudanças nos formulários, garantindo que os dados fossem capturados e armazenados corretamente.

### Próximos passos

Para as próximas versões da aplicação, planejamos focar em:

- Desenvolvimento do CSS para aprimorar a interface visual, tornando-a mais atrativa e consistente com o guia de estilos.

- Melhorias no fluxo entre telas, buscando uma navegação mais intuitiva e eficiente para o usuário.

- Otimização da performance, com melhorias nas consultas ao banco de dados e no carregamento das páginas, visando uma experiência mais fluida para o usuário.

## 4.2. Segunda versão da aplicação web (sprint 4)

*Descreva e ilustre aqui o desenvolvimento da sua segunda versão do sistema web, explicando brevemente o que foi entregue em termos de código e sistema. Utilize prints de tela para ilustrar. Indique as eventuais dificuldades e próximos passos.*

## 4.3. Versão final da aplicação web (sprint 5)

*Descreva e ilustre aqui o desenvolvimento da última versão do sistema web, explicando brevemente o que foi entregue em termos de código e sistema. Utilize prints de tela para ilustrar. Indique as eventuais dificuldades e próximos passos.*

# <a name="c5"></a>5. Testes

## 5.1. Relatório de testes de integração de endpoints automatizados (sprint 4)

*Liste e descreva os testes unitários dos endpoints criados, automatizados e planejados para sua solução. Posicione aqui também o relatório de cobertura de testes Jest se houver (através de link ou transcrito para estrutura markdown)*

## 5.2. Testes de usabilidade (sprint 5)

*Posicione aqui as tabelas com enunciados de tarefas, etapas e resultados de testes de usabilidade. Ou utilize um link para seu relatório de testes (mantenha o link sempre público para visualização)*

# <a name="c6"></a>6. Estudo de Mercado e Plano de Marketing (sprint 4)

## 6.1 Resumo Executivo

*Preencher com até 300 palavras, sem necessidade de fonte*

*Apresente de forma clara e objetiva os principais destaques do projeto: oportunidades de mercado, diferenciais competitivos da aplicação web e os objetivos estratégicos pretendidos.*

## 6.2 Análise de Mercado

*a) Visão Geral do Setor (até 250 palavras)*
*Contextualize o setor no qual a aplicação está inserida, considerando aspectos econômicos, tecnológicos e regulatórios. Utilize fontes confiáveis.*

*b) Tamanho e Crescimento do Mercado (até 250 palavras)*
*Apresente dados quantitativos sobre o tamanho atual e projeções de crescimento do mercado. Utilize fontes confiáveis.*

*c) Tendências de Mercado (até 300 palavras)*
*Identifique e analise tendências relevantes (tecnológicas, comportamentais e mercadológicas) que influenciam o setor. Utilize fontes confiáveis.*

## 6.3 Análise da Concorrência

*a) Principais Concorrentes (até 250 palavras)*
*Liste os concorrentes diretos e indiretos, destacando suas principais características e posicionamento no mercado.*

*b) Vantagens Competitivas da Aplicação Web (até 250 palavras)*
*Descreva os diferenciais da sua aplicação em relação aos concorrentes, sem necessidade de citação de fontes.*


## 6.4 Público-Alvo

*a) Segmentação de Mercado (até 250 palavras)*
Descreva os principais segmentos de mercado a serem atendidos pela aplicação. Utilize bases de dados e fontes confiáveis.*

*b) Perfil do Público-Alvo (até 250 palavras)*
*Caracterize o público-alvo com dados demográficos, psicográficos e comportamentais, incluindo necessidades específicas. Utilize fontes obrigatórias.*


## 6.5 Posicionamento

*a) Proposta de Valor Única (até 250 palavras)*
*Defina de maneira clara o que torna a sua aplicação única e valiosa para o mercado.*

*b) Estratégia de Diferenciação (até 250 palavras)*
*Explique como sua aplicação se destacará da concorrência, evidenciando a lógica por trás do posicionamento.*

## 6.6 Estratégia de Marketing

*a) Produto/Serviço (até 200 palavras)*
*Descreva as funcionalidades, benefícios e diferenciais da aplicação*

*6.2 Preço (até 200 palavras)*
*Explique o modelo de precificação adotado e justifique com base nas análises anteriores.*

*6.3 Praça (Distribuição) (até 200 palavras)*
*Apresente os canais digitais utilizados para distribuir e entregar a aplicação ao público.*

*6.4 Promoção (até 200 palavras)*
*Descreva as estratégias digitais planejadas, como SEO, redes sociais, marketing de conteúdo e campanhas pagas.*

# <a name="c7"></a>7. Conclusões e trabalhos futuros (sprint 5)

*Escreva de que formas a solução da aplicação web atingiu os objetivos descritos na seção 2 deste documento. Indique pontos fortes e pontos a melhorar de maneira geral.*

*Relacione os pontos de melhorias evidenciados nos testes com planos de ações para serem implementadas. O grupo não precisa implementá-las, pode deixar registrado aqui o plano para ações futuras*

*Relacione também quaisquer outras ideias que o grupo tenha para melhorias futuras*

# <a name="c8"></a>8. Referências (sprints 1 a 5)

<a id="cohn"></a>COHN, Mike. User Stories Applied: For Agile Software Development. Boston: Addison-Wesley, 2004.

<a id ='codd'></a>CODD, Edgar Frank. The Relational Model for Database Management: Version 2. Reading: Addison-Wesley, 1990.

<a id="ipt"></a>IPT. Licitações, [s.d.]. Disponível em: https://suprimentos.ipt.br/Licitacoes. Acesso em: 23 abr. 2025.

<a id="iso"></a>ISO31000.NET. Matriz de riscos: Definição, exemplos e aplicação prática. Disponível em: https://iso31000.net/matriz-de-riscos. Acesso em: 28 abr. 2025.

<a id="lee"></a>LEE, J.; FLEMING, C. Measuring the Impact of Evidence‑Based Personas on Product Development Outcomes. Cambridge, MA: MIT Integrated Design & Management Program, 2023.

<a id="luck"></a>LUCK, Heloisa. Liderança em gestão escolar. 4. ed. Petrópolis: Vozes, 2010.

<a id="osterwalder"></a>OSTERWALDER, A.; PIGNEUR, Y. Value Proposition Design: como criar produtos e serviços que seus clientes desejam. Rio de Janeiro: Alta Books, 2014.

<a id="porter"></a>PORTER, Michael E. Competitive strategy: techniques for analyzing industries and competitors. New York: Free Press, 1980.

<a id="wireframes"></a> GARRETT, Jesse James. The Elements of User Experience: User-Centered Design for the Web and Beyond. 2. ed. Berkeley: New Riders, 2011.

# <a name="c9"></a>Anexos

*Inclua aqui quaisquer complementos para seu projeto, como diagramas, imagens, tabelas etc. Organize em sub-tópicos utilizando headings menores (use ## ou ### para isso)*
