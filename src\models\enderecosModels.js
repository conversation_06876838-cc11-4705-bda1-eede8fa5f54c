// A tabela "enderecos" armazena os dados de localização das edificações.
// Cada endereço inclui CEP, rua, bairro, número, cidade e estado.

const db = require('../config/db');

class Endereco {
  static async todos() {
    const result = await db.query('SELECT * FROM enderecos');
    return result.rows;
  }

  static async buscarPorId(id) {
    const result = await db.query('SELECT * FROM enderecos WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async criar(data) {
    const result = await db.query(
      'INSERT INTO enderecos (cep, rua, bairro, num, cidade, estado) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',
      [data.cep, data.rua, data.bairro, data.num, data.cidade, data.estado]
    );
    return result.rows[0];
  }

  static async atualizar(id, data) {
    const result = await db.query(
      'UPDATE enderecos SET cep = $1, rua = $2, bairro = $3, num = $4, cidade = $5, estado = $6 WHERE id = $7 RETURNING *',
      [data.cep, data.rua, data.bairro, data.num, data.cidade, data.estado, id]
    );
    return result.rows[0];
  }

  static async deletar(id) {
    const result = await db.query('DELETE FROM enderecos WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = Endereco;
