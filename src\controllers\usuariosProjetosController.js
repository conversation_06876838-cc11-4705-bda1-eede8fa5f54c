const UsuariosProjetos = require('../models/usuariosProjetosModels');

// Lista todos os vínculos
const listarTodos = async (req, res) => {
  try {
    const vinculos = await UsuariosProjetos.todos();
    res.json(vinculos);
  } catch (error) {
    console.error('Erro ao listar vínculos:', error);
    res.status(500).json({ error: 'Erro ao listar vínculos' });
  }
};

// Busca vínculo por ID
const buscarPorId = async (req, res) => {
  const { id } = req.params;
  try {
    const vinculo = await UsuariosProjetos.buscarPorId(id);
    if (!vinculo) {
      return res.status(404).json({ error: 'Vínculo não encontrado' });
    }
    res.json(vinculo);
  } catch (error) {
    console.error('Erro ao buscar vínculo:', error);
    res.status(500).json({ error: 'Erro ao buscar vínculo' });
  }
};

// Cria novo vínculo
const criar = async (req, res) => {
  const { usuario_id, projeto_id } = req.body;

  if (!usuario_id || !projeto_id) {
    return res.status(400).json({ error: 'Os campos "usuario_id" e "projeto_id" são obrigatórios' });
  }

  try {
    const novoVinculo = await UsuariosProjetos.criar({ usuario_id, projeto_id });
    res.status(201).json(novoVinculo);
  } catch (error) {
    console.error('Erro ao criar vínculo:', error);
    res.status(500).json({ error: 'Erro ao criar vínculo' });
  }
};

// Atualiza vínculo
const atualizar = async (req, res) => {
  const { id } = req.params;
  const { usuario_id, projeto_id } = req.body;

  if (!usuario_id || !projeto_id) {
    return res.status(400).json({ error: 'Os campos "usuario_id" e "projeto_id" são obrigatórios' });
  }

  try {
    const vinculoAtualizado = await UsuariosProjetos.atualizar(id, { usuario_id, projeto_id });
    if (!vinculoAtualizado) {
      return res.status(404).json({ error: 'Vínculo não encontrado' });
    }
    res.json(vinculoAtualizado);
  } catch (error) {
    console.error('Erro ao atualizar vínculo:', error);
    res.status(500).json({ error: 'Erro ao atualizar vínculo' });
  }
};

// Deleta vínculo
const deletar = async (req, res) => {
  const { id } = req.params;
  try {
    const sucesso = await UsuariosProjetos.deletar(id);
    if (!sucesso) {
      return res.status(404).json({ error: 'Vínculo não encontrado' });
    }
    res.json({ message: 'Vínculo removido com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar vínculo:', error);
    res.status(500).json({ error: 'Erro ao deletar vínculo' });
  }
};

module.exports = {
  listarTodos,
  buscarPorId,
  criar,
  atualizar,
  deletar
};
