const Registros = require('../models/registrosModels');

// Lista todos os registros com dados relacionados
const listarTodos = async (req, res) => {
  try {
    const registros = await Registros.todos();
    res.json(registros);
  } catch (error) {
    console.error('Erro ao listar registros:', error);
    res.status(500).json({ error: 'Erro ao listar registros' });
  }
};

// Busca um registro pelo ID
const buscarPorId = async (req, res) => {
  const { id } = req.params;

  try {
    const registro = await Registros.buscarPorId(id);
    if (!registro) {
      return res.status(404).json({ error: 'Registro não encontrado' });
    }
    res.json(registro);
  } catch (error) {
    console.error('Erro ao buscar registro:', error);
    res.status(500).json({ error: 'Erro ao buscar registro' });
  }
};

// Cria um novo registro
const criar = async (req, res) => {
  const {
    cabecalho,
    torre_bloco,
    inspecao_id,
    ambiente_id,
    sistema_patologia_id,
    status_id,
    data_registro,
    observacoes
  } = req.body;

  try {
    const novoRegistro = await Registros.criar({
      cabecalho,
      torre_bloco,
      inspecao_id,
      ambiente_id,
      sistema_patologia_id,
      status_id,
      data_registro,
      observacoes
    });
    res.status(201).json(novoRegistro);
  } catch (error) {
    console.error('Erro ao criar registro:', error);
    res.status(500).json({ error: 'Erro ao criar registro' });
  }
};

// Atualiza um registro existente
const atualizar = async (req, res) => {
  const { id } = req.params;
  const {
    cabecalho,
    torre_bloco,
    inspecao_id,
    ambiente_id,
    sistema_patologia_id,
    status_id,
    data_registro,
    observacoes
  } = req.body;

  try {
    const registroExistente = await Registros.buscarPorId(id);
    if (!registroExistente) {
      return res.status(404).json({ error: 'Registro não encontrado' });
    }

    const registroAtualizado = await Registros.atualizar(id, {
      cabecalho,
      torre_bloco,
      inspecao_id,
      ambiente_id,
      sistema_patologia_id,
      status_id,
      data_registro,
      observacoes
    });

    res.json(registroAtualizado);
  } catch (error) {
    console.error('Erro ao atualizar registro:', error);
    res.status(500).json({ error: 'Erro ao atualizar registro' });
  }
};

// Deleta um registro pelo ID
const deletar = async (req, res) => {
  const { id } = req.params;

  try {
    const registroDeletado = await Registros.deletar(id);
    if (!registroDeletado) {
      return res.status(404).json({ error: 'Registro não encontrado' });
    }
    res.json({ message: 'Registro removido com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar registro:', error);
    res.status(500).json({ error: 'Erro ao deletar registro' });
  }
};

module.exports = {
  listarTodos,
  buscarPorId,
  criar,
  atualizar,
  deletar
};
