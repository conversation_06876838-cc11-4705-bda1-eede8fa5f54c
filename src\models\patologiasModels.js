const db = require('../config/db');

class Patologias {
  static async listarTodos() {
    const result = await db.query('SELECT * FROM patologias');
    return result.rows;
  }

  static async criar({descricao, categoria_id}) {
    const result = await db.query(
      'INSERT INTO patologias (descricao, categoria_id) VALUES ($1, $2) RETURNING *',
      [descricao, categoria_id]
    );
    return result.rows[0];
  }

  static async buscarPorId(id) {
    const result = await db.query('SELECT * FROM patologias WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async atualizar(id, {descricao, categoria_id}) {
    const result = await db.query(
      'UPDATE patologias SET descricao = $1, categoria_id = $2 WHERE id = $3 RETURNING *',
      [descricao, categoria_id, id]
    );
    return result.rows[0];
  }

  static async deletar(id) {
    const result = await db.query('DELETE FROM patologias WHERE id = $1 RETURNING *', [id]);
    return result.rows[0];
  }
}

module.exports = Patologias;