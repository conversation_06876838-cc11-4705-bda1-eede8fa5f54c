@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap');

body {
  font-family: 'Poppins', sans-serif;
  margin: 0; /* remove espaçamento padrão do body */
  height: 100vh; /* garante que a sidebar ocupe toda a tela */
  overflow: hidden; /* evita barra de rolagem indesejada */
}

.barraLateral {
  width: 60px; /* tamanho pequeno */
  transition: width 0.3s;
  background: #f1f3f6;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* logo/menu em cima e usuário embaixo */
  overflow: hidden;
  padding: 1rem 0.5rem;
  position: fixed; /* fixa na tela */
  top: 0;
  left: 0;
}

.barraLateral:hover {
  width: 200px; /* tamanho grande */
}

.logo {
  margin-bottom: 1rem;
}

.logo img {
  width: 65px;
}

.menu {
  flex-grow: 1;
  
}

.menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu li {
  margin: 1rem 0;
}

.menu a {
  display: flex;
  color: #000;
  text-decoration: none;
}


.menu img {
  width: 35px;
  height: 35px;
}

.menu .texto {
  opacity: 0;
  transition: opacity 0.3s;
  white-space: nowrap;
}

.barraLateral:hover .menu .texto,
.barraLateral:hover .usuario .texto {
  opacity: 1;
}

.usuario {
  display: flex;
  align-items: center;
  padding-bottom: 2.5rem; /* empurra o usuário pra cima */
  gap: 0.5rem;
}

.usuario img {
  width: 40px;
  border-radius: 50%;
}

.usuario .texto {
  opacity: 0;
  transition: opacity 0.3s;
  white-space: nowrap;
}
