<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>Ambiente</title>
  <style>
    * { box-sizing: border-box; }
    body {
      font-family: sans-serif;
      margin: 0;
      background: #fff;
      padding-bottom: 120px;
    }

    header {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px;
      font-weight: bold;
      font-size: 1.3rem;
      border-bottom: 1px solid #ddd;
      position: relative;
    }

    .back-btn {
      position: absolute;
      left: 16px;
      font-size: 24px;
      cursor: pointer;
    }

    main {
      max-width: 480px;
      margin: 0 auto;
      padding: 1rem;
    }

    .planta-container img {
      width: 100%;
      border-radius: 12px;
    }

    .planta-container p {
      text-align: center;
      font-size: 0.9rem;
      color: #777;
      margin-top: 6px;
      font-style: italic;
    }

    textarea {
      width: 100%;
      padding: 12px;
      border-radius: 10px;
      border: 1px solid #ccc;
      margin-top: 16px;
      font-size: 0.95rem;
    }

    h3 {
      margin-top: 32px;
      font-size: 1rem;
    }

    .btn {
      margin-top: 24px;
      width: 100%;
      background-color: #f72585;
      color: white;
      font-size: 1rem;
      font-weight: bold;
      padding: 14px 0;
      border: none;
      border-radius: 16px;
      cursor: pointer;
    }

    .bottom-nav {
      position: fixed;
      bottom: 0;
      width: 100%;
      background-color: #f5f5f5;
      display: flex;
      justify-content: space-around;
      padding: 12px 0;
      border-top: 1px solid #ddd;
    }

    .bottom-nav div {
      text-align: center;
      font-size: 14px;
      cursor: pointer;
    }

    .bottom-nav img {
      width: 24px;
      height: auto;
      margin: 0 auto 4px;
      display: block;
    }
  </style>
</head>
<body>

  <header>
    <span class="back-btn" onclick="history.back()">×</span>
    Ambiente
  </header>

  <main>
    <div class="planta-container">
      <img id="planta-img" src="../assets/planta.png" alt="Planta baixa">
      <p>Desenhe na imagem acima o local onde a patologia foi registrada</p>
    </div>

    <h3>Caso não haja planta, descreva aqui sua posição</h3>
    <textarea id="descricao-local" placeholder="Adicione uma descrição para a sua localização"></textarea>

    <button class="btn" onclick="window.location.href='registro.html'">ADICIONAR LOCALIZAÇÃO</button>
  </main>

  <div class="bottom-nav">
    <div onclick="location.href='inspecoes.html'">
      <img src="../assets/Icons/paste.png" alt="Inspeções">
      <div>Inspeções</div>
    </div>
    <div onclick="location.href='../views/perfil.html'">
      <img src="../assets/Icons/profile.png" alt="Perfil">
      <div>Perfil</div>
    </div>
  </div>

  <script>
    function adicionarLocal() {
      const texto = document.getElementById('descricao-local').value.trim();
      const localSalvo = texto !== "" ? texto : "Selecionado na planta";
      localStorage.setItem('localPatologia', localSalvo);
      window.location.href = 'registro.html';
    }
  </script>

</body>
</html>
