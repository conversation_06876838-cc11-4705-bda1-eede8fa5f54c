const fs = require('fs');
const path = require('path');
const client = require('../../config/db'); // conexão com o banco

const sql = fs.readFileSync(path.join(__dirname, '../../sql/development/20250509_create_usuarios.sql')).toString();

async function run() {
    try {
    await client.connect();
    await client.query(sql);
    console.log(' Tabela usuarios criada com sucesso.');
    } catch (err) {
    console.error(' Erro ao criar tabela usuarios:', err);
    } finally {
    await client.end();
    }
}

run();
