const db = require('../config/db');

class CategoriaPatologia {
  static async todos() {
    const result = await db.query('SELECT * FROM categorias_patologias');
    return result.rows;
  }

  static async buscarPorId(id) {
    const result = await db.query('SELECT * FROM categorias_patologias WHERE id = $1', [id]);
    return result.rows[0];
  }

  static async criar(data) {
    const result = await db.query(
      'INSERT INTO categorias_patologias (nome, descricao) VALUES ($1, $2) RETURNING *',
      [data.nome, data.descricao]
    );
    return result.rows[0];
  }

  static async atualizar(id, data) {
    const result = await db.query(
      'UPDATE categorias_patologias SET nome = $1, descricao = $2 WHERE id = $3 RETURNING *',
      [data.nome, data.descricao, id]
    );
    return result.rows[0];
  }

  static async deletar(id) {
    const result = await db.query('DELETE FROM categorias_patologias WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = CategoriaPatologia;
