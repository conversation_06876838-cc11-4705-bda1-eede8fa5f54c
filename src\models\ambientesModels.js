const db = require('../config/db');

class Ambiente {
  // Lista todos os ambientes
  static async todos() {
    const result = await db.query('SELECT * FROM ambientes');
    return result.rows;
  }

  // Busca um ambiente pelo ID
  static async buscarPorId(id) {
    const result = await db.query('SELECT * FROM ambientes WHERE id = $1', [id]);
    return result.rows[0];
  }

  // Insere um novo ambiente
  static async criar(ambiente) {
    const result = await db.query(
      'INSERT INTO ambientes (nome) VALUES ($1) RETURNING *',
      [ambiente.nome]
    );
    return result.rows[0];
  }

  // Atualiza um ambiente existente
  static async atualizar(id, dados) {
    const result = await db.query(
      'UPDATE ambientes SET nome = $1 WHERE id = $2 RETURNING *',
      [dados.nome, id]
    );
    return result.rows[0];
  }

  // Deleta um ambiente pelo ID
  static async deletar(id) {
    const result = await db.query('DELETE FROM ambientes WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = Ambiente;
