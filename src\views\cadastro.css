body {
    background-color: #00558C;
}

h1 {
    font-family: 'Inter', Arial, sans-serif;
    font-size: 40px; /* Ajuste conforme necessário */
    font-weight: 400; /* Inter Regular */
    color: #FFFFFF; /* Branco */
    text-align: center;
}

.caixaMaior {
    display: flex;
    height: 100vh;
    width: 100%;
    background-color: #00558C;
    justify-content: center;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.ladoEsquerdo {
    flex: none;
    width: 30%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;  /* alinhamento à esquerda */
    justify-content: center;
    gap: 20px;
    padding-left: 50px;
}

/* Lado direito - <PERSON>ulá<PERSON> */
.caixaCadastro {
    width: 420px;
    background-color: #1a4c7a;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 40px;
    margin: 60px 80px 60px 40px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

form {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.inputGrupo {
    position: relative;
    width: 100%;
}

.inputGrupo input {
    width: 100%;
    padding: 15px 15px 15px 50px;
    border: none;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    font-family: 'Inter', Arial, sans-serif;
    color: #333;
}

.inputGrupo input::placeholder {
    color: #000000;
    font-size: 16px;
}


button {
    width: 60%;
    padding: 15px 25px;
    border: none;
    border-radius: 25px;
    background-color: #2563eb;
    color: #FFFFFF;
    font-size: 20px;
    font-weight: 400;
    font-family: 'Inter', Arial, sans-serif;
    cursor: pointer;
    margin: 20px auto 0;
    transition: background-color 0.3s ease;
    display: block;
}

button:hover {
    background-color: #1d4ed8;
}

button:active {
    transform: translateY(1px);
}

.inputGrupo {
    display: flex;
    align-items: center;
    gap: 10px; /* Espaço entre ícone e input */
}

.icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    color: #666;
    z-index: 1;
}

/* Responsividade */
/* Responsividade para tablets */
@media (max-width: 768px) {
    .caixaMaior {
        flex-direction: column;
        gap: 30px;
        padding: 30px 20px;
        min-height: auto;
    }
}

/* Responsividade para celulares */
@media (max-width: 480px) {
    body {
        padding: 10px;
    }
    
    .caixaMaior {
        padding: 20px 15px;
        gap: 20px;
        border-radius: 16px;
    }
}

.ladoEsquerdo {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 40px;
    padding: 20px;
}

/* Responsividade para tablets e celulares */
@media (max-width: 768px) {
    .ladoEsquerdo {
        flex: none;
        gap: 20px;
        padding: 10px;
    }
    
    .logoBatman, .logoIPT {
        width: 50%;
        max-width: 200px;
    }
}

@media (max-width: 480px) {
    .ladoEsquerdo {
        gap: 15px;
    }
    
    .logoBatman, .logoIPT {
        width: 60%;
        max-width: 150px;
    }
}

.logoBatman, .logoIPT {
    width: 70%;
    max-width: 250px;
    height: auto;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    transition: transform 0.3s ease;
}

.logoBatman:hover, .logoIPT:hover {
    transform: scale(1.05);
}

.caixaLogin {
    flex: 1;
    max-width: 450px;
    background: rgba(26, 76, 122, 0.9);
    backdrop-filter: blur(20px);
    padding: 50px 40px;
    border-radius: 24px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* Responsividade para tablets e celulares */
@media (max-width: 768px) {
    .caixaLogin {
        flex: none;
        width: 100%;
        max-width: 500px;
        padding: 40px 30px;
    }
}

@media (max-width: 480px) {
    .caixaLogin {
        padding: 30px 20px;
        border-radius: 16px;
        max-width: none;
    }
}

h1 {
    color: #fff;
    text-align: center;
    font-size: clamp(28px, 5vw, 40px);
    font-weight: 600;
    margin-bottom: 40px;
    letter-spacing: -0.5px;
}

/* Responsividade para o título */
@media (max-width: 480px) {
    h1 {
        margin-bottom: 30px;
    }
}

form {
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;
}

/* Responsividade para o formulário */
@media (max-width: 480px) {
    form {
        gap: 20px;
    }
}

.inputGrupo {
    position: relative;
    width: 100%;
}

.inputGrupo input {
    width: 100%;
    padding: 18px 20px 18px 55px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    transition: all 0.3s ease;
    outline: none;
}

/* Responsividade para inputs */
@media (max-width: 480px) {
    .inputGrupo input {
        padding: 16px 18px 16px 50px;
        font-size: 16px; /* Previne zoom no iOS */
    }
}

.inputGrupo input:focus {
    border-color: #2563eb;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.inputGrupo input::placeholder {
    color: #888;
    font-weight: 400;
}

.icon {
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.inputGrupo:focus-within .icon {
    opacity: 1;
}

.esqueceuSenha {
    text-align: center;
    margin-bottom: 5px;
    margin-top: 10px;
}

.esqueceuSenha a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: underline;
    font-size: clamp(14px, 2.5vw, 16px);
    transition: color 0.3s ease;
}

.esqueceuSenha a:hover {
    color: #fff;
}

button {
    width: 100%;
    padding: 18px;
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
    position: relative;
    overflow: hidden;
}

/* Responsividade para botão */
@media (max-width: 480px) {
    button {
        padding: 16px;
        font-size: 16px;
    }
}

button:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
}

button:active {
    transform: translateY(0);
}

.inscrevaSe {
    text-align: center;
    margin-top: 20px;
}

.inscrevaSe a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: underline;
    font-size: clamp(14px, 2.5vw, 16px);
    transition: color 0.3s ease;
}

.inscrevaSe a:hover {
    color: #fff;
}