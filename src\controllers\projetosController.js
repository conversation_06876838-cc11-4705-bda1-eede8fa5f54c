const Projetos = require('../models/projetosModels');

// Lista todos os projetos
const listarTodos = async (req, res) => {
  try {
    const projetos = await Projetos.todos();
    res.json(projetos);
  } catch (error) {
    console.error('Erro ao listar projetos:', error);
    res.status(500).json({ error: 'Erro ao listar projetos' });
  }
};

// Busca um projeto pelo ID
const buscarPorId = async (req, res) => {
  const { id } = req.params;

  try {
    const projeto = await Projetos.buscarPorId(id);
    if (!projeto) {
      return res.status(404).json({ error: 'Projeto não encontrado' });
    }
    res.json(projeto);
  } catch (error) {
    console.error('Erro ao buscar projeto:', error);
    res.status(500).json({ error: 'Erro ao buscar projeto' });
  }
};

// Cria um novo projeto
const criar = async (req, res) => {
  const { cabecalho, edificio_id, coordenador_id, data_inicio, data_fim } = req.body;

  try {
    const novoProjeto = await Projetos.criar({ cabecalho, edificio_id, coordenador_id, data_inicio, data_fim });
    res.status(201).json(novoProjeto);
  } catch (error) {
    console.error('Erro ao criar projeto:', error);
    res.status(500).json({ error: 'Erro ao criar projeto' });
  }
};

// Atualiza um projeto existente
const atualizar = async (req, res) => {
  const { id } = req.params;
  const { cabecalho, edificio_id, coordenador_id, data_inicio, data_fim } = req.body;

  try {
    const projetoExistente = await Projetos.buscarPorId(id);
    if (!projetoExistente) {
      return res.status(404).json({ error: 'Projeto não encontrado' });
    }

    const projetoAtualizado = await Projetos.atualizar(id, { cabecalho, edificio_id, coordenador_id, data_inicio, data_fim });
    res.json(projetoAtualizado);
  } catch (error) {
    console.error('Erro ao atualizar projeto:', error);
    res.status(500).json({ error: 'Erro ao atualizar projeto' });
  }
};

// Deleta um projeto pelo ID
const deletar = async (req, res) => {
  const { id } = req.params;

  try {
    const projetoDeletado = await Projetos.deletar(id);
    if (!projetoDeletado) {
      return res.status(404).json({ error: 'Projeto não encontrado' });
    }
    res.json({ message: 'Projeto removido com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar projeto:', error);
    res.status(500).json({ error: 'Erro ao deletar projeto' });
  }
};

module.exports = {
  listarTodos,
  buscarPorId,
  criar,
  atualizar,
  deletar,
};
