const HistoricoStatusInspecao = require('../models/HistoricoStatusInspecao');

class HistoricoStatusController {
  // Lista todos os registros de histórico de status
  static async todos(req, res) {
    try {
      const historicos = await HistoricoStatusInspecao.todos();
      res.json(historicos);
    } catch (error) {
      res.status(500).json({ error: 'Erro ao buscar históricos de status' });
    }
  }

  // Busca um registro de histórico específico por ID
  static async buscarPorId(req, res) {
    try {
      const { id } = req.params;
      const historico = await HistoricoStatusInspecao.buscarPorId(id);
      
      if (!historico) {
        return res.status(404).json({ error: 'Histórico de status não encontrado' });
      }
      
      res.json(historico);
    } catch (error) {
      res.status(500).json({ error: 'Erro ao buscar histórico de status' });
    }
  }

  // Cria um novo registro de histórico de status
  static async criar(req, res) {
    try {
      const { inspecao_id, status, data } = req.body;
      
      if (!inspecao_id || !status) {
        return res.status(400).json({ error: 'Campos obrigatórios não preenchidos' });
      }

      const novoHistorico = await HistoricoStatusInspecao.criar({
        inspecao_id,
        status,
        data: data || new Date() // Se não for fornecida uma data, usa a data atual
      });

      res.status(201).json(novoHistorico);
    } catch (error) {
      res.status(500).json({ error: 'Erro ao criar histórico de status' });
    }
  }

  // Atualiza um registro de histórico existente
  static async atualizar(req, res) {
    try {
      const { id } = req.params;
      const { inspecao_id, status, data } = req.body;

      if (!inspecao_id || !status) {
        return res.status(400).json({ error: 'Campos obrigatórios não preenchidos' });
      }

      const historicoAtualizado = await HistoricoStatusInspecao.atualizar(id, {
        inspecao_id,
        status,
        data: data || new Date()
      });

      if (!historicoAtualizado) {
        return res.status(404).json({ error: 'Histórico de status não encontrado' });
      }

      res.json(historicoAtualizado);
    } catch (error) {
      res.status(500).json({ error: 'Erro ao atualizar histórico de status' });
    }
  }

  // Remove um registro de histórico
  static async deletar(req, res) {
    try {
      const { id } = req.params;
      const deletado = await HistoricoStatusInspecao.deletar(id);

      if (!deletado) {
        return res.status(404).json({ error: 'Histórico de status não encontrado' });
      }

      res.status(204).send();
    } catch (error) {
      res.status(500).json({ error: 'Erro ao deletar histórico de status' });
    }
  }
}

module.exports = HistoricoStatusController;
