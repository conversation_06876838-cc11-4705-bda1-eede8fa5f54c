const db = require('../config/db');

class PatologiaAnexo {
  // Lista todos os registros patologia_anexos
  static async todos() {
    const result = await db.query('SELECT * FROM patologia_anexos');
    return result.rows;
  }

  // Busca um registro pelo ID
  static async buscarPorId(id) {
    const result = await db.query('SELECT * FROM patologia_anexos WHERE id = $1', [id]);
    return result.rows[0];
  }

  // Cria um novo registro patologia_anexo
  static async criar(patologiaAnexo) {
    const result = await db.query(
      'INSERT INTO patologia_anexos (patologia_id, anexo_id) VALUES ($1, $2) RETURNING *',
      [patologiaAnexo.patologia_id, patologiaAnexo.anexo_id]
    );
    return result.rows[0];
  }

  // Atualiza um registro existente
  static async atualizar(id, dados) {
    const result = await db.query(
      'UPDATE patologia_anexos SET patologia_id = $1, anexo_id = $2 WHERE id = $3 RETURNING *',
      [dados.patologia_id, dados.anexo_id, id]
    );
    return result.rows[0];
  }

  // Deleta um registro pelo ID
  static async deletar(id) {
    const result = await db.query('DELETE FROM patologia_anexos WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = PatologiaAnexo;
