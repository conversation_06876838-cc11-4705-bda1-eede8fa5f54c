// Importa o model Anexo, que contém os métodos para manipular os dados no banco
const Anexo = require('../models/anexosModels');

// Função para listar todos os anexos
const listarAnexos = async (req, res) => {
  try {
    const anexos = await Anexo.todos();
    res.status(200).json(anexos);
  } catch (error) {
    console.error('Erro ao buscar anexos:', error);
    res.status(500).json({ erro: 'Erro ao buscar anexos.' });
  }
};

// Função para buscar um anexo pelo ID passado na URL
const buscarAnexoPorId = async (req, res) => {
  try {
    const anexo = await Anexo.buscarPorId(req.params.id);
    if (anexo) {
      res.status(200).json(anexo);
    } else {
      res.status(404).json({ erro: 'Anexo não encontrado.' });
    }
  } catch (error) {
    console.error('Erro ao buscar o anexo:', error);
    res.status(500).json({ erro: 'Erro ao buscar o anexo.' });
  }
};

// Função para criar um novo anexo, recebendo dados via corpo da requisição
const criarAnexo = async (req, res) => {
  try {
    const novoAnexo = await Anexo.criar(req.body);
    res.status(201).json(novoAnexo);
  } catch (error) {
    console.error('Erro ao criar o anexo:', error);
    res.status(500).json({ erro: 'Erro ao criar o anexo.' });
  }
};

// Função para atualizar um anexo existente pelo ID
const atualizarAnexo = async (req, res) => {
  try {
    const anexoAtualizado = await Anexo.atualizar(req.params.id, req.body);
    if (anexoAtualizado) {
      res.status(200).json(anexoAtualizado);
    } else {
      res.status(404).json({ erro: 'Anexo não encontrado para atualização.' });
    }
  } catch (error) {
    console.error('Erro ao atualizar o anexo:', error);
    res.status(500).json({ erro: 'Erro ao atualizar o anexo.' });
  }
};

// Função para deletar um anexo pelo ID
const deletarAnexo = async (req, res) => {
  try {
    const sucesso = await Anexo.deletar(req.params.id);
    if (sucesso) {
      res.status(200).json({ mensagem: 'Anexo deletado com sucesso.' });
    } else {
      res.status(404).json({ erro: 'Anexo não encontrado para exclusão.' });
    }
  } catch (error) {
    console.error('Erro ao deletar o anexo:', error);
    res.status(500).json({ erro: 'Erro ao deletar o anexo.' });
  }
};

// Exporta as funções para serem usadas nas rotas
module.exports = {
  listarAnexos,
  buscarAnexoPorId,
  criarAnexo,
  atualizarAnexo,
  deletarAnexo,
};