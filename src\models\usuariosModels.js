// A tabela "usuarios" armazena os dados dos usuários do sistema,
// incluindo nome, e-mail e senha criptografada.
// O campo "email" deve ser único para evitar duplicidade de contas.

const db = require('../config/db');

class Usuarios {
  // Lista todos os usuários
  static async todos() {
    const result = await db.query('SELECT * FROM usuarios');
    return result.rows;
  }

  // Busca um usuário pelo ID
  static async buscarPorId(id) {
    const result = await db.query('SELECT * FROM usuarios WHERE id = $1', [id]);
    return result.rows[0];
  }

  // Busca um usuário pelo e-mail (útil para login)
  static async buscarPorEmail(email) {
    const result = await db.query('SELECT * FROM usuarios WHERE email = $1', [email]);
    return result.rows[0];
  }

  // Cria um novo usuário
  static async criar({ nome, email, senha }) {
    const result = await db.query(
      'INSERT INTO usuarios (nome, email, senha) VALUES ($1, $2, $3) RETURNING *',
      [nome, email, senha]
    );
    return result.rows[0];
  }

  // Atualiza os dados de um usuário
  static async atualizar(id, { nome, email, senha }) {
    const result = await db.query(
      'UPDATE usuarios SET nome = $1, email = $2, senha = $3 WHERE id = $4 RETURNING *',
      [nome, email, senha, id]
    );
    return result.rows[0];
  }

  // Remove um usuário pelo ID
  static async deletar(id) {
    const result = await db.query('DELETE FROM usuarios WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = Usuarios;
