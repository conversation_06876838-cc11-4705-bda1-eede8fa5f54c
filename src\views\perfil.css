body {
  margin: 0;
  font-family: sans-serif;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  padding: 16px;
  font-weight: bold;
  border-bottom: 1px solid #ccc;
}

.back-button {
  font-size: 24px;
  background: none;
  border: none;
  cursor: pointer;
  margin-right: 16px;
}

.container {
  padding: 16px;
  max-width: 480px;
  margin: 0 auto;
}

.profile {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  margin-bottom: 24px;
}

.profile-img {
  width: 96px;
  height: 96px;
  border-radius: 8px;
  object-fit: cover;
}

.profile-info label {
  font-weight: bold;
  display: block;
  margin-bottom: 4px;
  margin-top: 8px;
}

.profile-info input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 8px;
  margin-bottom: 8px;
  background-color: #f9f9f9;
}

.section-title {
  font-size: 18px;
  margin: 16px 0 8px;
  border-top: 1px solid #ccc;
  padding-top: 16px;
}

.inspection-card {
  display: flex;
  gap: 12px;
  background: #f0f0f0;
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 16px;
  align-items: center;
}

.inspection-img {
  width: 100px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
}

.inspection-content h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
}

.inspection-content p {
  margin: 0;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.status-nao {
  background-color: #555;
}

.status-andamento {
  background-color: #fbbf24;
  color: black;
}

.bottom-nav {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: space-around;
  padding: 12px 0;
  border-top: 1px solid #ddd;
}

.bottom-nav div {
  text-align: center;
  font-size: 14px;
  cursor: pointer;
}

.bottom-nav img {
  width: 24px;
  height: auto;
  display: block;
  margin: 0 auto 4px;
}
