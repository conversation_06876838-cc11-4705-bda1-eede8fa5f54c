const CategoriaPatologia = require('../models/categoriaPatologiasModels');

// Lista todas as categorias de patologia
const listarTodas = async (req, res) => {
  try {
    const categorias = await CategoriaPatologia.todos();
    res.json(categorias);
  } catch (error) {
    console.error('Erro ao listar categorias de patologia:', error);
    res.status(500).json({ error: 'Erro ao listar categorias de patologia' });
  }
};

// Busca categoria por ID
const buscarPorId = async (req, res) => {
  const { id } = req.params;
  try {
    const categoria = await CategoriaPatologia.buscarPorId(id);
    if (!categoria) {
      return res.status(404).json({ error: 'Categoria de patologia não encontrada' });
    }
    res.json(categoria);
  } catch (error) {
    console.error('Erro ao buscar categoria de patologia:', error);
    res.status(500).json({ error: 'Erro ao buscar categoria de patologia' });
  }
};

// Cria nova categoria de patologia
const criar = async (req, res) => {
  const { nome, descricao } = req.body;

  if (!nome || !descricao) {
    return res.status(400).json({ error: 'Os campos "nome" e "descricao" são obrigatórios' });
  }

  try {
    const novaCategoria = await CategoriaPatologia.criar({ nome, descricao });
    res.status(201).json(novaCategoria);
  } catch (error) {
    console.error('Erro ao criar categoria de patologia:', error);
    res.status(500).json({ error: 'Erro ao criar categoria de patologia' });
  }
};

// Atualiza categoria existente
const atualizar = async (req, res) => {
  const { id } = req.params;
  const { nome, descricao } = req.body;

  if (!nome || !descricao) {
    return res.status(400).json({ error: 'Os campos "nome" e "descricao" são obrigatórios' });
  }

  try {
    const categoriaAtualizada = await CategoriaPatologia.atualizar(id, { nome, descricao });
    if (!categoriaAtualizada) {
      return res.status(404).json({ error: 'Categoria de patologia não encontrada' });
    }
    res.json(categoriaAtualizada);
  } catch (error) {
    console.error('Erro ao atualizar categoria de patologia:', error);
    res.status(500).json({ error: 'Erro ao atualizar categoria de patologia' });
  }
};

// Deleta categoria
const deletar = async (req, res) => {
  const { id } = req.params;

  try {
    const sucesso = await CategoriaPatologia.deletar(id);
    if (!sucesso) {
      return res.status(404).json({ error: 'Categoria de patologia não encontrada' });
    }
    res.json({ message: 'Categoria de patologia deletada com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar categoria de patologia:', error);
    res.status(500).json({ error: 'Erro ao deletar categoria de patologia' });
  }
};

module.exports = {
  listarTodas,
  buscarPorId,
  criar,
  atualizar,
  deletar
};
