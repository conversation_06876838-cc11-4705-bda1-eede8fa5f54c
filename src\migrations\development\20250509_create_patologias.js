    const fs = require('fs');
    const path = require('path');
    const client = require('../../config/db');

    const sql = fs.readFileSync(path.join(__dirname, '../../sql/development/20250509_create_patologias.sql')).toString();

    async function run() {
    try {
        await client.connect();
        await client.query(sql);
        console.log('Tabela patologias criada com sucesso.');
    } catch (err) {
        console.error('Erro ao criar tabela patologias:', err);
    } finally {
        await client.end();
    }
    }

    run();
