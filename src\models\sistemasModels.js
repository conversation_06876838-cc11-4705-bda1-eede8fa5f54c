// A tabela "sistemas" representa os diferentes sistemas construtivos de uma edificação,
// como hidráulico, elétrico ou estrutural. Essa categorização técnica é usada para
// classificar onde ocorrem as patologias durante as inspeções prediais.

const db = require('../config/db');

class Sistemas {
  // Lista todos os sistemas
  static async todos() {
    const result = await db.query('SELECT * FROM sistemas');
    return result.rows;
  }

  // Busca um sistema pelo ID
  static async buscarPorId(id) {
    const result = await db.query('SELECT * FROM sistemas WHERE id = $1', [id]);
    return result.rows[0];
  }

  // Insere um novo sistema
  static async criar(sistema) {
    const result = await db.query(
      'INSERT INTO sistemas (nome) VALUES ($1) RETURNING *',
      [sistema.nome]
    );
    return result.rows[0];
  }

  // Atualiza um sistema existente
  static async atualizar(id, dados) {
    const result = await db.query(
      'UPDATE sistemas SET nome = $1 WHERE id = $2 RETURNING *',
      [dados.nome, id]
    );
    return result.rows[0];
  }

  // Deleta um sistema pelo ID
  static async deletar(id) {
    const result = await db.query('DELETE FROM sistemas WHERE id = $1 RETURNING *', [id]);
    return result.rowCount > 0;
  }
}

module.exports = Sistemas;
