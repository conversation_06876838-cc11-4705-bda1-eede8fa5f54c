const Endereco = require('../models/enderecosModels');

const enderecoController = {
  // Lista todos os endereços
  async listarTodos(req, res) {
    try {
      const enderecos = await Endereco.todos();
      res.json(enderecos);
    } catch (error) {
      console.error('Erro ao listar endereços:', error);
      res.status(500).json({ error: 'Erro ao listar endereços' });
    }
  },

  // Busca endereço por ID
  async buscarPorId(req, res) {
    const { id } = req.params;
    try {
      const endereco = await Endereco.buscarPorId(id);
      if (!endereco) {
        return res.status(404).json({ error: 'Endereço não encontrado' });
      }
      res.json(endereco);
    } catch (error) {
      console.error('Erro ao buscar endereço:', error);
      res.status(500).json({ error: 'Erro ao buscar endereço' });
    }
  },

  // Cria um novo endereço
  async criar(req, res) {
    const { cep, rua, bairro, num, cidade, estado } = req.body;

    if (!cep || !rua || !bairro || !num || !cidade || !estado) {
      return res.status(400).json({ error: 'Todos os campos são obrigatórios' });
    }

    try {
      const novoEndereco = await Endereco.criar({ cep, rua, bairro, num, cidade, estado });
      res.status(201).json(novoEndereco);
    } catch (error) {
      console.error('Erro ao criar endereço:', error);
      res.status(500).json({ error: 'Erro ao criar endereço' });
    }
  },

  // Atualiza um endereço existente
  async atualizar(req, res) {
    const { id } = req.params;
    const { cep, rua, bairro, num, cidade, estado } = req.body;

    if (!cep || !rua || !bairro || !num || !cidade || !estado) {
      return res.status(400).json({ error: 'Todos os campos são obrigatórios' });
    }

    try {
      const enderecoExistente = await Endereco.buscarPorId(id);
      if (!enderecoExistente) {
        return res.status(404).json({ error: 'Endereço não encontrado' });
      }

      const enderecoAtualizado = await Endereco.atualizar(id, { cep, rua, bairro, num, cidade, estado });
      res.json(enderecoAtualizado);
    } catch (error) {
      console.error('Erro ao atualizar endereço:', error);
      res.status(500).json({ error: 'Erro ao atualizar endereço' });
    }
  },

  // Deleta um endereço
  async deletar(req, res) {
    const { id } = req.params;
    try {
      const sucesso = await Endereco.deletar(id);
      if (!sucesso) {
        return res.status(404).json({ error: 'Endereço não encontrado' });
      }
      res.json({ message: 'Endereço removido com sucesso' });
    } catch (error) {
      console.error('Erro ao deletar endereço:', error);
      res.status(500).json({ error: 'Erro ao deletar endereço' });
    }
  },
};

module.exports = enderecoController;
