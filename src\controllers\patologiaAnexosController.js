const PatologiaAnexo = require('../models/patologiaAnexosModels');

// Lista todos os registros
const listar = async (req, res) => {
  try {
    const registros = await PatologiaAnexo.todos();
    res.json(registros);
  } catch (error) {
    console.error('Erro ao listar patologia_anexos:', error);
    res.status(500).json({ error: 'Erro ao listar patologia_anexos' });
  }
};

// Busca um registro pelo ID
const buscarPorId = async (req, res) => {
  const { id } = req.params;

  try {
    const registro = await PatologiaAnexo.buscarPorId(id);
    if (!registro) {
      return res.status(404).json({ error: 'Registro não encontrado' });
    }
    res.json(registro);
  } catch (error) {
    console.error('Erro ao buscar patologia_anexo:', error);
    res.status(500).json({ error: 'Erro ao buscar patologia_anexo' });
  }
};

// Cria um novo registro
const criar = async (req, res) => {
  const { patologia_id, anexo_id } = req.body;

  try {
    const novoRegistro = await PatologiaAnexo.criar({ patologia_id, anexo_id });
    res.status(201).json(novoRegistro);
  } catch (error) {
    console.error('Erro ao criar patologia_anexo:', error);
    res.status(500).json({ error: 'Erro ao criar patologia_anexo' });
  }
};

// Atualiza um registro existente
const atualizar = async (req, res) => {
  const { id } = req.params;
  const { patologia_id, anexo_id } = req.body;

  try {
    const registroExistente = await PatologiaAnexo.buscarPorId(id);
    if (!registroExistente) {
      return res.status(404).json({ error: 'Registro não encontrado' });
    }

    const registroAtualizado = await PatologiaAnexo.atualizar(id, { patologia_id, anexo_id });
    res.json(registroAtualizado);
  } catch (error) {
    console.error('Erro ao atualizar patologia_anexo:', error);
    res.status(500).json({ error: 'Erro ao atualizar patologia_anexo' });
  }
};

// Deleta um registro pelo ID
const deletar = async (req, res) => {
  const { id } = req.params;

  try {
    const sucesso = await PatologiaAnexo.deletar(id);
    if (!sucesso) {
      return res.status(404).json({ error: 'Registro não encontrado' });
    }
    res.json({ message: 'Registro removido com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar patologia_anexo:', error);
    res.status(500).json({ error: 'Erro ao deletar patologia_anexo' });
  }
};

module.exports = {
  listar,
  buscarPorId,
  criar,
  atualizar,
  deletar,
};
