const express = require('express');
const router = express.Router();

const {
  listarTodas,
  buscarPorId,
  criar,
  atualizar,
  deletar,
} = require('../controllers/categoriasPatologiasController');

// GET /categorias-patologias - Lista todas as categorias
router.get('/', listarTodas);

// GET /categorias-patologias/:id - Busca categoria por ID
router.get('/:id', buscarPorId);

// POST /categorias-patologias - Cria uma nova categoria
router.post('/', criar);

// PUT /categorias-patologias/:id - Atualiza uma categoria existente
router.put('/:id', atualizar);

// DELETE /categorias-patologias/:id - Deleta uma categoria
router.delete('/:id', deletar);

module.exports = router;
