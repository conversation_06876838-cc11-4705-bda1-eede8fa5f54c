const express = require('express');
const router = express.Router();

const {
  listarAnexos,
  buscarAnexoPorId,
  criarAnexo,
  atualizarAnexo,
  deletarAnexo,
} = require('../controllers/anexosController');

// Lista todos os anexos
router.get('/', listarAnexos);

// Busca um anexo pelo ID
router.get('/:id', buscarAnexoPorId);

// Cria um novo anexo
router.post('/', criarAnexo);

// Atualiza um anexo existente
router.put('/:id', atualizarAnexo);

// Deleta um anexo
router.delete('/:id', deletarAnexo);

module.exports = router;
