const express = require('express');
const router = express.Router();
const usuariosProjetosController = require('../controllers/usuariosProjetosController');

router.get('/', usuariosProjetosController.listarTodos);
router.get('/:id', usuariosProjetosController.buscarPorId);
router.post('/', usuariosProjetosController.criar);
router.put('/:id', usuariosProjetosController.atualizar);
router.delete('/:id', usuariosProjetosController.deletar);

module.exports = router;
