<!DOCTYPE html>
<html lang="pt-br">
<head>
  <link rel="stylesheet" href="registro.css">

  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Registro #01</title>
  <style>
    * { box-sizing: border-box; }
    body { font-family: sans-serif; margin: 0; padding: 0; background: #fff; }
    header { display: flex; align-items: center; justify-content: space-between; padding: 16px; font-weight: bold; }
    .close { font-size: 24px; cursor: pointer; }
    .container { padding: 16px; padding-bottom: 100px; max-width: 480px; margin: 0 auto; }
    .image-upload {
      border: 1px solid #ccc;
      border-radius: 8px;
      text-align: center;
      padding: 24px;
      margin-bottom: 24px;
      position: relative;
    }
    .plus-circle {
      display: block;
      margin: 0 auto 8px;
      width: 80px;
      height: 80px;
      cursor: pointer;
    }
    .plus-circle img { width: 100%; height: auto; }
    .image-upload input[type="file"] {
      display: none;
    }
    label { font-weight: bold; display: block; margin-top: 16px; margin-bottom: 8px; }
    input, textarea {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 8px;
      margin-bottom: 8px;
    }
    .arrow-link {
      text-align: right;
      margin-top: -40px;
      margin-bottom: 16px;
      padding-right: 8px;
      font-size: 18px;
    }
    .arrow-link a {
      text-decoration: none;
      color: inherit;
    }
    .btn {
      width: 100%;
      border: none;
      padding: 0;
      background: none;
      margin-top: 24px;
      cursor: pointer;
    }
    .btn img {
      width: 100%;
      max-width: 300px;
      display: block;
      margin: 0 auto;
    }
    .bottom-nav {
      position: fixed;
      bottom: 0;
      width: 100%;
      background-color: #f5f5f5;
      display: flex;
      justify-content: space-around;
      padding: 12px 0;
      border-top: 1px solid #ddd;
    }
    .bottom-nav div { text-align: center; font-size: 14px; cursor: pointer; }
    .bottom-nav img { width: 24px; height: auto; display: block; margin: 0 auto 4px; }
    #preview {
      width: 100%;
      max-height: 200px;
      object-fit: contain;
      display: none;
      margin-top: 12px;
    }
  </style>
</head>
<body>
  <header>
    <span class="close" onclick="window.history.back()">&times;</span>
    <span>Inspeção BPO</span>
    <span style="visibility:hidden">X</span>
  </header>
  <div class="container">
    <h2>Registro #01</h2>
    <div class="image-upload">
      <label for="uploadInput" class="plus-circle">
        <img src="/assets/botoes/add.png" alt="Adicionar imagem">
      </label>
      <input type="file" id="uploadInput" accept="image/*" onchange="previewImage(event)">
      <p>Adicione uma imagem do Ambiente com a Patologia</p>
      <img id="preview" alt="Preview da imagem">
    </div>

    <label for="tipo">Tipo de patologia</label>
    <a href="../views/filtroPatologia.html">
      <input type="text" id="tipo" placeholder="Selecione o tipo de patologia" readonly>
    </a>
    <div class="arrow-link"><a href="./views/filtroPatologia.html">&#8250;</a></div>

    <label for="desc">Descrição</label>
    <textarea id="desc" rows="4" placeholder="Adicione uma descrição da manifestação patológica"></textarea>

    <label for="ambiente">Ambiente</label>
    <input type="text" id="ambiente" placeholder="Indique o local onde a patologia foi encontrada." readonly onclick="window.location.href='ambiente.html'">
    <div class="arrow-link"><a href="ambiente.html">&#8250;</a></div>

    <button class="btn" onclick="window.location.href='relatorio.html'">
      <img src="../assets/botoes/criar.png" alt="Botão Criar">
    </button>
  </div>

  <div class="bottom-nav">
    <div onclick="location.href='../views/inspecoes.html'">
      <img src="../assets/Icons/paste.png" alt="Inspeções">
      <div>Inspeções</div>
    </div>
    <div onclick="location.href='../views/perfil.html'">
      <img src="../assets/Icons/profile.png" alt="Perfil">
      <div>Perfil</div>
    </div>
  </div>

  <script>
    function previewImage(event) {
      const preview = document.getElementById('preview');
      preview.src = URL.createObjectURL(event.target.files[0]);
      preview.style.display = 'block';
      localStorage.setItem('imagemInspecao', preview.src);
    }

    function submitForm() {
      const tipo = document.getElementById('tipo').value;
      const desc = document.getElementById('desc').value;
      const ambiente = document.getElementById('ambiente').value;

      if (!tipo || !desc || !ambiente) {
        alert('Preencha todos os campos!');
        return;
      }

      alert('Registro criado com sucesso!');
    }

    document.addEventListener("DOMContentLoaded", () => {
      const tipo = localStorage.getItem('patologiaSelecionada');
      if (tipo) {
        document.getElementById('tipo').value = tipo;
      }

      const local = localStorage.getItem('localPatologia');
      if (local) {
        document.getElementById('ambiente').value = local;
      }
    });
  </script>
</body>
</html>
