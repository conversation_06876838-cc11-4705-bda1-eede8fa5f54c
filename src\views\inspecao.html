<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>Inspeção BPO</title>
  <style>
    * { box-sizing: border-box; }
    body {
      font-family: sans-serif;
      margin: 0;
      padding-bottom: 140px; /* espaço p/ botão + rodapé */
      background: #fff;
    }

    header {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px;
      font-size: 1.3rem;
      font-weight: bold;
      border-bottom: 1px solid #ddd;
      position: relative;
    }

    .back-btn {
      position: absolute;
      left: 16px;
      font-size: 24px;
      cursor: pointer;
    }

    main {
      max-width: 480px;
      margin: 0 auto;
      padding: 1rem;
    }

    .top-info {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .top-info img {
      width: 120px;
      height: 90px;
      object-fit: cover;
      border-radius: 12px;
    }

    .info-text {
      font-size: 0.95rem;
    }

    .info-text strong {
      display: block;
      font-size: 1rem;
      margin-bottom: 4px;
    }

    .add-btn {
      display: block;
      width: 100%;
      background-color: #00bdf2;
      color: white;
      text-align: center;
      padding: 12px;
      border-radius: 12px;
      text-decoration: none;
      font-weight: bold;
      margin: 1rem 0;
    }

    h2 {
      font-size: 1.1rem;
      margin-bottom: 1rem;
    }

    .registro {
      display: flex;
      align-items: center;
      background-color: #f1f3f5;
      padding: 0.8rem;
      border-radius: 12px;
      margin-bottom: 1rem;
    }

    .registro img {
      width: 50px;
      height: 50px;
      border-radius: 8px;
      object-fit: cover;
    }

    .registro .avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      margin-left: 8px;
    }

    .registro .info {
      margin-left: 12px;
    }

    .registro .info strong {
      display: block;
      font-size: 1rem;
    }

    .registro .info span {
      font-size: 0.85rem;
      color: #555;
    }

    .finalizar-btn {
      position: fixed;
      bottom: 64px;
      left: 50%;
      transform: translateX(-50%);
      width: 90%;
      background-color: #f72585;
      color: white;
      text-align: center;
      padding: 14px;
      border: none;
      border-radius: 999px;
      font-size: 1rem;
      font-weight: bold;
      cursor: pointer;
      z-index: 10;
    }

    .bottom-nav {
      position: fixed;
      bottom: 0;
      width: 100%;
      background-color: #f5f5f5;
      display: flex;
      justify-content: space-around;
      padding: 12px 0;
      border-top: 1px solid #ddd;
    }

    .bottom-nav div {
      text-align: center;
      font-size: 14px;
      cursor: pointer;
    }

    .bottom-nav img {
      width: 24px;
      height: auto;
      display: block;
      margin: 0 auto 4px;
    }
  </style>
</head>
<body>

  <header>
    <span class="back-btn" onclick="history.back()">←</span>
    Inspeção BPO
  </header>

  <main>
    <div class="top-info">
      <img src="https://via.placeholder.com/120x90" alt="Prédio">
      <div class="info-text">
        <strong>Prédio Industrial Rima</strong>
        <span>22/07/2025 - 29/09/2025</span><br />
        <span>Rua das Flores, 123, São Paulo - SP</span>
      </div>
    </div>

    <a href="registro.html" class="add-btn">Adicionar patologia</a>

    <h2>Registos</h2>

    <!-- Registros repetidos para exemplo -->
    <div class="registro">
      <img src="https://via.placeholder.com/50x50?text=📷" alt="Imagem patologia">
      <img class="avatar" src="https://via.placeholder.com/36x36?text=👤" alt="Avatar">
      <div class="info">
        <strong>Fissura na parede</strong>
        <span>22/07/2024 · 10:00</span>
      </div>
    </div>

    <div class="registro">
      <img src="https://via.placeholder.com/50x50?text=📷" alt="Imagem patologia">
      <img class="avatar" src="https://via.placeholder.com/36x36?text=👤" alt="Avatar">
      <div class="info">
        <strong>Fissura na parede</strong>
        <span>22/07/2024 · 10:00</span>
      </div>
    </div>

    <div class="registro">
      <img src="https://via.placeholder.com/50x50?text=📷" alt="Imagem patologia">
      <img class="avatar" src="https://via.placeholder.com/36x36?text=👤" alt="Avatar">
      <div class="info">
        <strong>Fissura na parede</strong>
        <span>22/07/2024 · 10:00</span>
      </div>
    </div>
  </main>

  <button class="finalizar-btn" onclick="window.location.href='relatorio.html'">Finalizar</button>

  <div class="bottom-nav">
    <div onclick="location.href='inspecoes.html'">
      <img src="../assets/Icons/paste.png" alt="Inspeções">
      <div>Inspeções</div>
    </div>
    <div onclick="location.href='../views/perfil.html'">
      <img src="../assets/Icons/profile.png" alt="Perfil">
      <div>Perfil</div>
    </div>
  </div>

</body>
</html>
