const EtapaInspecao = require('../models/etapasInspecaoModels');

// Lista todas as etapas de inspeção
const listarEtapas = async (req, res) => {
  try {
    const etapas = await EtapaInspecao.todos();
    res.status(200).json(etapas);
  } catch (error) {
    console.error('Erro ao listar etapas de inspeção:', error);
    res.status(500).json({ error: 'Erro ao listar etapas de inspeção' });
  }
};

// Busca uma etapa de inspeção pelo ID
const buscarEtapaPorId = async (req, res) => {
  const { id } = req.params;
  try {
    const etapa = await EtapaInspecao.buscarPorId(id);
    if (!etapa) {
      return res.status(404).json({ error: 'Etapa de inspeção não encontrada' });
    }
    res.status(200).json(etapa);
  } catch (error) {
    console.error('Erro ao buscar etapa de inspeção:', error);
    res.status(500).json({ error: 'Erro ao buscar etapa de inspeção' });
  }
};

// Cria uma nova etapa de inspeção
const criarEtapa = async (req, res) => {
  const { inspecao_id, etapa, data_inicio, data_fim } = req.body;

  try {
    const novaEtapa = await EtapaInspecao.criar({ inspecao_id, etapa, data_inicio, data_fim });
    res.status(201).json(novaEtapa);
  } catch (error) {
    console.error('Erro ao criar etapa de inspeção:', error);
    res.status(500).json({ error: 'Erro ao criar etapa de inspeção' });
  }
};

// Atualiza uma etapa de inspeção
const atualizarEtapa = async (req, res) => {
  const { id } = req.params;
  const { inspecao_id, etapa, data_inicio, data_fim } = req.body;

  try {
    const etapaExistente = await EtapaInspecao.buscarPorId(id);
    if (!etapaExistente) {
      return res.status(404).json({ error: 'Etapa de inspeção não encontrada' });
    }

    const etapaAtualizada = await EtapaInspecao.atualizar(id, { inspecao_id, etapa, data_inicio, data_fim });
    res.status(200).json(etapaAtualizada);
  } catch (error) {
    console.error('Erro ao atualizar etapa de inspeção:', error);
    res.status(500).json({ error: 'Erro ao atualizar etapa de inspeção' });
  }
};

// Deleta uma etapa de inspeção
const deletarEtapa = async (req, res) => {
  const { id } = req.params;

  try {
    const sucesso = await EtapaInspecao.deletar(id);
    if (!sucesso) {
      return res.status(404).json({ error: 'Etapa de inspeção não encontrada' });
    }
    res.status(200).json({ message: 'Etapa de inspeção removida com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar etapa de inspeção:', error);
    res.status(500).json({ error: 'Erro ao deletar etapa de inspeção' });
  }
};

module.exports = {
  listarEtapas,
  buscarEtapaPorId,
  criarEtapa,
  atualizarEtapa,
  deletarEtapa,
};
